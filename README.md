# Receipt Parser Pro

Advanced receipt parsing tool that combines multiple OCR and AI technologies for 100% accuracy:
- **Tesseract OCR** with advanced image preprocessing
- **Docling** for intelligent document structure analysis
- **Docling PDF Pipeline** for PDF receipt processing
- **Google Gemini AI** for intelligent data extraction and validation

## Features

- 🔍 **Multi-Method Parsing**: Combines Tesseract, Docling, and PDF pipeline results
- 🤖 **AI-Powered Validation**: Uses Google Gemini AI for intelligent data extraction
- 📊 **Consensus System**: Determines the most accurate result from multiple methods
- 🖼️ **Image Enhancement**: Advanced preprocessing for optimal OCR accuracy
- 📄 **PDF Support**: Native PDF processing with Docling pipeline
- 🎯 **High Accuracy**: Designed for 100% accuracy through multi-method consensus
- 🔧 **Easy Integration**: CLI and REST API interfaces

## Installation

1. **Install System Dependencies**:
   ```bash
   # Ubuntu/Debian
   sudo apt-get install tesseract-ocr tesseract-ocr-eng
   
   # macOS
   brew install tesseract
   
   # Windows
   # Download and install from: https://github.com/UB-Mannheim/tesseract/wiki
   ```

2. **Install Python Package**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Setup Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your Google Gemini API key
   ```

## Quick Start

```python
from receipt_parser import ReceiptParser

# Initialize parser
parser = ReceiptParser()

# Parse receipt from image
result = parser.parse_receipt("receipt.jpg")

# Parse receipt from PDF
result = parser.parse_receipt("receipt.pdf")

print(result.to_dict())
```

## CLI Usage

```bash
# Parse single receipt
receipt-parser parse receipt.jpg

# Parse multiple receipts
receipt-parser batch receipts/

# Start REST API server
receipt-parser serve --host 0.0.0.0 --port 8000
```

## Configuration

See `.env.example` for all configuration options.

## License

MIT License
