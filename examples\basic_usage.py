#!/usr/bin/env python3
"""
Basic usage examples for Receipt Parser Pro.
"""

import json
from pathlib import Path
from receipt_parser import Receipt<PERSON><PERSON><PERSON>, Config, ParsingMethod


def main():
    """Demonstrate basic usage of Receipt Parser Pro."""
    
    print("🧾 Receipt Parser Pro - Basic Usage Examples")
    print("=" * 50)
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        config = Config.load_config()
        
        # Initialize parser
        print("🚀 Initializing parser...")
        parser = ReceiptParser(config)
        
        # Example 1: Parse a single image receipt
        print("\n📸 Example 1: Parse image receipt")
        image_path = Path("samples/receipt_image.jpg")
        
        if image_path.exists():
            print(f"   Processing: {image_path}")
            result = parser.parse_receipt(image_path)
            
            if result.success:
                print(f"   ✅ Success! Confidence: {result.confidence:.2f}")
                if result.data:
                    print(f"   🏪 Merchant: {result.data.merchant_name}")
                    print(f"   💰 Total: {result.data.total_amount}")
                    print(f"   📅 Date: {result.data.transaction_date}")
            else:
                print(f"   ❌ Failed: {result.error_message}")
        else:
            print(f"   ⚠️  Sample file not found: {image_path}")
        
        # Example 2: Parse a PDF receipt
        print("\n📄 Example 2: Parse PDF receipt")
        pdf_path = Path("samples/receipt.pdf")
        
        if pdf_path.exists():
            print(f"   Processing: {pdf_path}")
            result = parser.parse_receipt(pdf_path)
            
            if result.success:
                print(f"   ✅ Success! Confidence: {result.confidence:.2f}")
                if result.data:
                    print(f"   🏪 Merchant: {result.data.merchant_name}")
                    print(f"   💰 Total: {result.data.total_amount}")
            else:
                print(f"   ❌ Failed: {result.error_message}")
        else:
            print(f"   ⚠️  Sample file not found: {pdf_path}")
        
        # Example 3: Parse with specific methods
        print("\n🔧 Example 3: Parse with specific methods")
        sample_path = Path("samples/receipt_image.jpg")
        
        if sample_path.exists():
            print(f"   Processing with Tesseract only: {sample_path}")
            result = parser.parse_receipt(
                sample_path, 
                methods=[ParsingMethod.TESSERACT],
                use_consensus=False
            )
            
            if result.success:
                print(f"   ✅ Tesseract result - Confidence: {result.confidence:.2f}")
            else:
                print(f"   ❌ Tesseract failed: {result.error_message}")
        
        # Example 4: Parse with consensus
        print("\n🤝 Example 4: Parse with consensus from multiple methods")
        
        if sample_path.exists():
            print(f"   Processing with all methods + consensus: {sample_path}")
            result = parser.parse_receipt(sample_path, use_consensus=True)
            
            if hasattr(result, 'consensus_score'):
                print(f"   ✅ Consensus result - Score: {result.consensus_score:.2f}")
                print(f"   📊 Individual results: {len(result.individual_results)}")
                
                for individual_result in result.individual_results:
                    status = "✅" if individual_result.success else "❌"
                    print(f"      {status} {individual_result.method.value}: {individual_result.confidence:.2f}")
            else:
                print(f"   ✅ Single method result - Confidence: {result.confidence:.2f}")
        
        # Example 5: Export results to JSON
        print("\n💾 Example 5: Export results to JSON")
        
        if sample_path.exists():
            result = parser.parse_receipt(sample_path)
            
            if result.success:
                # Export to JSON
                output_file = Path("output/parsed_receipt.json")
                output_file.parent.mkdir(exist_ok=True)
                
                if hasattr(result, 'final_result'):
                    export_data = {
                        "consensus_result": result.final_result.dict(),
                        "individual_results": [r.dict() for r in result.individual_results],
                        "consensus_score": result.consensus_score
                    }
                else:
                    export_data = result.dict()
                
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, default=str)
                
                print(f"   💾 Results exported to: {output_file}")
            else:
                print(f"   ❌ No results to export")
        
        print("\n🎉 Examples completed!")
        print("\n💡 Tips:")
        print("   • Set GOOGLE_API_KEY environment variable for Gemini AI")
        print("   • Install Tesseract OCR for image processing")
        print("   • Use high-quality images for best results")
        print("   • PDF receipts work best with Docling PDF pipeline")
        
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        print("\n🔧 Troubleshooting:")
        print("   • Check that all dependencies are installed")
        print("   • Verify your Google API key is set")
        print("   • Ensure Tesseract is installed and in PATH")


if __name__ == "__main__":
    main()
