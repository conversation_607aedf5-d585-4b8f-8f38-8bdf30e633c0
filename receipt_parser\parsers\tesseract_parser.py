"""
Tesseract OCR parser with advanced image preprocessing.
"""

import cv2
import numpy as np
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter
from pathlib import Path
from typing import Optional, Tuple
from loguru import logger
import pdf2image

from ..config import Config


class TesseractParser:
    """
    Tesseract OCR parser with advanced image preprocessing for optimal accuracy.
    """
    
    def __init__(self, config: Config):
        """Initialize Tesseract parser with configuration."""
        self.config = config
        
        # Set tesseract command path
        if config.tesseract_cmd != "tesseract":
            pytesseract.pytesseract.tesseract_cmd = config.tesseract_cmd
        
        logger.info("Tesseract parser initialized")
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from image or PDF using Tesseract OCR.
        
        Args:
            file_path: Path to the image or PDF file
            
        Returns:
            Extracted text string
        """
        try:
            # Handle PDF files
            if file_path.suffix.lower() == '.pdf':
                return self._extract_text_from_pdf(file_path)
            else:
                return self._extract_text_from_image(file_path)
                
        except Exception as e:
            logger.error(f"Error extracting text with Tesseract: {str(e)}")
            raise
    
    def _extract_text_from_pdf(self, pdf_path: Path) -> str:
        """Extract text from PDF by converting to images first."""
        logger.info(f"Converting PDF to images: {pdf_path}")
        
        # Convert PDF to images
        images = pdf2image.convert_from_path(
            pdf_path,
            dpi=300,  # High DPI for better OCR
            first_page=1,
            last_page=5  # Limit to first 5 pages for receipts
        )
        
        all_text = []
        for i, image in enumerate(images):
            logger.info(f"Processing PDF page {i+1}")
            
            # Convert PIL image to numpy array for preprocessing
            img_array = np.array(image)
            
            # Preprocess and extract text
            processed_img = self._preprocess_image(img_array)
            text = self._ocr_image(processed_img)
            
            if text.strip():
                all_text.append(text)
        
        return "\n\n".join(all_text)
    
    def _extract_text_from_image(self, image_path: Path) -> str:
        """Extract text from image file."""
        logger.info(f"Processing image: {image_path}")
        
        # Load image
        image = cv2.imread(str(image_path))
        if image is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Preprocess image
        processed_image = self._preprocess_image(image)
        
        # Extract text
        text = self._ocr_image(processed_image)
        
        return text
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Apply advanced preprocessing to improve OCR accuracy.
        
        Args:
            image: Input image as numpy array
            
        Returns:
            Preprocessed image
        """
        # Resize if image is too large
        height, width = image.shape[:2]
        max_size = self.config.max_image_size
        
        if max(height, width) > max_size:
            scale = max_size / max(height, width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
            logger.info(f"Resized image to {new_width}x{new_height}")
        
        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()
        
        # Apply noise reduction
        denoised = cv2.fastNlMeansDenoising(gray)
        
        # Enhance contrast using CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(denoised)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(enhanced, (1, 1), 0)
        
        # Apply threshold to get binary image
        # Use Otsu's thresholding for automatic threshold selection
        _, binary = cv2.threshold(blurred, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
        
        # Morphological operations to clean up the image
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        # Deskew the image
        deskewed = self._deskew_image(cleaned)
        
        return deskewed
    
    def _deskew_image(self, image: np.ndarray) -> np.ndarray:
        """
        Detect and correct skew in the image.
        
        Args:
            image: Binary image
            
        Returns:
            Deskewed image
        """
        # Find all white pixels
        coords = np.column_stack(np.where(image > 0))
        
        if len(coords) < 10:  # Not enough points to determine skew
            return image
        
        # Find minimum area rectangle
        rect = cv2.minAreaRect(coords)
        angle = rect[2]
        
        # Determine the angle to rotate
        if angle < -45:
            angle = -(90 + angle)
        else:
            angle = -angle
        
        # Only correct if angle is significant (> 0.5 degrees)
        if abs(angle) > 0.5:
            logger.info(f"Correcting skew angle: {angle:.2f} degrees")
            
            # Get rotation matrix
            (h, w) = image.shape[:2]
            center = (w // 2, h // 2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            
            # Perform rotation
            rotated = cv2.warpAffine(
                image, M, (w, h), 
                flags=cv2.INTER_CUBIC, 
                borderMode=cv2.BORDER_REPLICATE
            )
            
            return rotated
        
        return image
    
    def _ocr_image(self, image: np.ndarray) -> str:
        """
        Perform OCR on preprocessed image.
        
        Args:
            image: Preprocessed image
            
        Returns:
            Extracted text
        """
        # Convert numpy array to PIL Image
        pil_image = Image.fromarray(image)
        
        # Apply additional PIL enhancements
        pil_image = self._enhance_pil_image(pil_image)
        
        # Perform OCR with custom configuration
        whitelist_chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!@#$%^&*()_+-=[]|;:,.<>?/~` "
        custom_config = f'{self.config.tesseract_config} -c tessedit_char_whitelist={whitelist_chars}'
        
        text = pytesseract.image_to_string(pil_image, config=custom_config)
        
        # Clean up the text
        cleaned_text = self._clean_text(text)
        
        logger.info(f"Extracted {len(cleaned_text)} characters of text")
        return cleaned_text
    
    def _enhance_pil_image(self, image: Image.Image) -> Image.Image:
        """Apply additional PIL-based enhancements."""
        # Enhance sharpness
        enhancer = ImageEnhance.Sharpness(image)
        image = enhancer.enhance(1.2)
        
        # Enhance contrast
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.1)
        
        return image
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = [line.strip() for line in text.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        # Join lines with single newlines
        cleaned = '\n'.join(lines)
        
        return cleaned
