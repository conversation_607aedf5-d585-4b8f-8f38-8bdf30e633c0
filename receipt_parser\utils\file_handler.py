"""
File handling utilities for receipt parsing.
"""

import mimetypes
from pathlib import Path
from typing import Optional, List
from loguru import logger

from ..config import Config


class FileHandler:
    """
    Utility class for handling file operations and type detection.
    """
    
    def __init__(self, config: Config):
        """Initialize file handler with configuration."""
        self.config = config
        
        # Supported file extensions
        self.image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.webp'}
        self.pdf_extensions = {'.pdf'}
        self.supported_extensions = self.image_extensions | self.pdf_extensions
        
        logger.info("File handler initialized")
    
    def get_file_type(self, file_path: Path) -> str:
        """
        Determine the file type based on extension and MIME type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            File type string ('image', 'pdf', or 'unknown')
        """
        try:
            # Check extension first
            extension = file_path.suffix.lower()
            
            if extension in self.pdf_extensions:
                return 'pdf'
            elif extension in self.image_extensions:
                return 'image'
            
            # Check MIME type as fallback
            mime_type, _ = mimetypes.guess_type(str(file_path))
            
            if mime_type:
                if mime_type.startswith('image/'):
                    return 'image'
                elif mime_type == 'application/pdf':
                    return 'pdf'
            
            logger.warning(f"Unknown file type for: {file_path}")
            return 'unknown'
            
        except Exception as e:
            logger.error(f"Error determining file type: {str(e)}")
            return 'unknown'
    
    def is_supported_file(self, file_path: Path) -> bool:
        """
        Check if the file is supported for parsing.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file is supported, False otherwise
        """
        if not file_path.exists():
            return False
        
        extension = file_path.suffix.lower()
        return extension in self.supported_extensions
    
    def validate_file(self, file_path: Path) -> tuple[bool, Optional[str]]:
        """
        Validate file for parsing.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check if file exists
            if not file_path.exists():
                return False, f"File does not exist: {file_path}"
            
            # Check if it's a file (not directory)
            if not file_path.is_file():
                return False, f"Path is not a file: {file_path}"
            
            # Check file size
            file_size = file_path.stat().st_size
            max_size = 50 * 1024 * 1024  # 50MB limit
            
            if file_size == 0:
                return False, "File is empty"
            
            if file_size > max_size:
                return False, f"File too large: {file_size / (1024*1024):.1f}MB (max: 50MB)"
            
            # Check if file type is supported
            if not self.is_supported_file(file_path):
                return False, f"Unsupported file type: {file_path.suffix}"
            
            return True, None
            
        except Exception as e:
            return False, f"Error validating file: {str(e)}"
    
    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return sorted(list(self.supported_extensions))
    
    def find_receipt_files(self, directory: Path, recursive: bool = False) -> List[Path]:
        """
        Find all supported receipt files in a directory.
        
        Args:
            directory: Directory to search
            recursive: Whether to search recursively
            
        Returns:
            List of supported file paths
        """
        try:
            if not directory.exists() or not directory.is_dir():
                logger.warning(f"Directory does not exist: {directory}")
                return []
            
            files = []
            
            if recursive:
                pattern = "**/*"
            else:
                pattern = "*"
            
            for file_path in directory.glob(pattern):
                if file_path.is_file() and self.is_supported_file(file_path):
                    is_valid, error = self.validate_file(file_path)
                    if is_valid:
                        files.append(file_path)
                    else:
                        logger.warning(f"Skipping invalid file {file_path}: {error}")
            
            logger.info(f"Found {len(files)} supported files in {directory}")
            return sorted(files)
            
        except Exception as e:
            logger.error(f"Error finding receipt files: {str(e)}")
            return []
    
    def create_output_filename(self, input_path: Path, suffix: str = "_parsed", extension: str = ".json") -> Path:
        """
        Create output filename based on input file.
        
        Args:
            input_path: Input file path
            suffix: Suffix to add to filename
            extension: Output file extension
            
        Returns:
            Output file path
        """
        stem = input_path.stem
        output_name = f"{stem}{suffix}{extension}"
        return input_path.parent / output_name
    
    def ensure_directory(self, directory: Path) -> bool:
        """
        Ensure directory exists, create if necessary.
        
        Args:
            directory: Directory path
            
        Returns:
            True if directory exists or was created successfully
        """
        try:
            directory.mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"Error creating directory {directory}: {str(e)}")
            return False
