"""
Pytest configuration and fixtures for receipt parser tests.
"""

import pytest
import tempfile
import json
from pathlib import Path
from PIL import Image
import numpy as np

from receipt_parser.config import Config
from receipt_parser.models import ReceiptData, ReceiptItem


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_config():
    """Create a sample configuration for testing."""
    return Config(
        google_api_key="test_api_key",
        tesseract_cmd="tesseract",
        tesseract_config="--oem 3 --psm 6",
        max_image_size=4096,
        confidence_threshold=0.8,
        consensus_threshold=0.9,
        log_level="INFO"
    )


@pytest.fixture
def sample_receipt_image(temp_dir):
    """Create a sample receipt image for testing."""
    # Create a simple test image
    image = Image.new('RGB', (400, 600), color='white')
    
    # Save to temporary file
    image_path = temp_dir / "sample_receipt.jpg"
    image.save(image_path)
    
    return image_path


@pytest.fixture
def sample_receipt_pdf(temp_dir):
    """Create a sample receipt PDF for testing."""
    # Create a simple PDF file (placeholder)
    pdf_path = temp_dir / "sample_receipt.pdf"
    
    # Create a minimal PDF content (this is just for testing file handling)
    pdf_content = b"%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\n3 0 obj\n<< /Type /Page /Parent 2 0 R /MediaBox [0 0 612 792] >>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<< /Size 4 /Root 1 0 R >>\nstartxref\n174\n%%EOF"
    
    with open(pdf_path, 'wb') as f:
        f.write(pdf_content)
    
    return pdf_path


@pytest.fixture
def sample_receipt_data():
    """Create sample receipt data for testing."""
    return ReceiptData(
        merchant_name="Test Grocery Store",
        merchant_address="123 Main St, Test City, TC 12345",
        merchant_phone="(*************",
        transaction_date="2025-06-30",
        transaction_time="14:30",
        receipt_number="TXN123456",
        items=[
            ReceiptItem(
                name="Apples",
                quantity=2.0,
                unit_price="1.99",
                total_price="3.98",
                category="Produce"
            ),
            ReceiptItem(
                name="Bread",
                quantity=1.0,
                unit_price="2.49",
                total_price="2.49",
                category="Bakery"
            ),
            ReceiptItem(
                name="Milk",
                quantity=1.0,
                unit_price="3.29",
                total_price="3.29",
                category="Dairy"
            )
        ],
        subtotal="9.76",
        tax_amount="0.78",
        total_amount="10.54",
        payment_method="Credit Card",
        card_last_four="1234",
        currency="USD",
        raw_text="Test Grocery Store\n123 Main St\nApples 2 @ $1.99 = $3.98\nBread $2.49\nMilk $3.29\nSubtotal: $9.76\nTax: $0.78\nTotal: $10.54"
    )


@pytest.fixture
def sample_raw_text():
    """Sample raw OCR text for testing."""
    return """
    Test Grocery Store
    123 Main St, Test City, TC 12345
    (*************
    
    Date: 06/30/2025    Time: 2:30 PM
    Receipt #: TXN123456
    
    Apples           2 @ $1.99    $3.98
    Bread                         $2.49
    Milk                          $3.29
    
    Subtotal:                     $9.76
    Tax:                          $0.78
    Total:                       $10.54
    
    Payment: Credit Card ****1234
    
    Thank you for shopping with us!
    """


@pytest.fixture
def sample_json_output(sample_receipt_data):
    """Sample JSON output for testing."""
    return {
        "merchant_name": sample_receipt_data.merchant_name,
        "merchant_address": sample_receipt_data.merchant_address,
        "merchant_phone": sample_receipt_data.merchant_phone,
        "transaction_date": "2025-06-30",
        "transaction_time": sample_receipt_data.transaction_time,
        "receipt_number": sample_receipt_data.receipt_number,
        "items": [
            {
                "name": "Apples",
                "quantity": 2.0,
                "unit_price": "1.99",
                "total_price": "3.98",
                "category": "Produce"
            },
            {
                "name": "Bread",
                "quantity": 1.0,
                "unit_price": "2.49",
                "total_price": "2.49",
                "category": "Bakery"
            },
            {
                "name": "Milk",
                "quantity": 1.0,
                "unit_price": "3.29",
                "total_price": "3.29",
                "category": "Dairy"
            }
        ],
        "subtotal": "9.76",
        "tax_amount": "0.78",
        "total_amount": "10.54",
        "payment_method": "Credit Card",
        "card_last_four": "1234",
        "currency": "USD"
    }


@pytest.fixture
def mock_gemini_response():
    """Mock response from Gemini AI for testing."""
    return {
        "merchant_name": "Test Store",
        "total_amount": "12.99",
        "transaction_date": "2025-06-30",
        "items": [
            {
                "name": "Test Item",
                "quantity": 1.0,
                "unit_price": "12.99",
                "total_price": "12.99"
            }
        ],
        "currency": "USD"
    }
