"""
Docling parser for intelligent document structure analysis.
"""

from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.document_converter import PdfFormatOption
except ImportError:
    logger.warning("Docling not available. Install with: pip install docling")
    DocumentConverter = None

from ..config import Config


class DoclingParser:
    """
    Docling parser for intelligent document structure analysis.
    """
    
    def __init__(self, config: Config):
        """Initialize Docling parser with configuration."""
        self.config = config
        
        if DocumentConverter is None:
            raise ImportError("Docling is not installed. Please install with: pip install docling")
        
        # Initialize document converter
        self.converter = DocumentConverter()
        
        logger.info("Docling parser initialized")
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from document using Docling.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Extracted text string
        """
        try:
            logger.info(f"Processing document with Docling: {file_path}")
            
            # Convert document
            result = self.converter.convert(str(file_path))
            
            if not result or not result.document:
                logger.warning("No document content extracted")
                return ""
            
            # Extract text content
            text_content = self._extract_text_from_document(result.document)
            
            logger.info(f"Extracted {len(text_content)} characters with Docling")
            return text_content
            
        except Exception as e:
            logger.error(f"Error extracting text with Docling: {str(e)}")
            raise
    
    def _extract_text_from_document(self, document) -> str:
        """
        Extract text from Docling document object.
        
        Args:
            document: Docling document object
            
        Returns:
            Extracted text
        """
        text_parts = []
        
        try:
            # Extract text from document structure
            if hasattr(document, 'body') and document.body:
                for element in document.body:
                    if hasattr(element, 'text') and element.text:
                        text_parts.append(element.text.strip())
                    elif hasattr(element, 'content') and element.content:
                        text_parts.append(str(element.content).strip())
            
            # If no structured content, try to get raw text
            if not text_parts and hasattr(document, 'text'):
                text_parts.append(document.text)
            
            # Join all text parts
            full_text = '\n'.join(text_parts)
            
            # Clean up the text
            cleaned_text = self._clean_text(full_text)
            
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Error processing Docling document structure: {str(e)}")
            # Fallback: try to convert document to string
            try:
                return str(document)
            except:
                return ""
    
    def extract_structured_data(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract structured data from document using Docling.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing structured document data
        """
        try:
            logger.info(f"Extracting structured data with Docling: {file_path}")
            
            # Convert document
            result = self.converter.convert(str(file_path))
            
            if not result or not result.document:
                return {}
            
            document = result.document
            structured_data = {}
            
            # Extract metadata
            if hasattr(document, 'metadata'):
                structured_data['metadata'] = document.metadata
            
            # Extract tables if present
            tables = self._extract_tables(document)
            if tables:
                structured_data['tables'] = tables
            
            # Extract text blocks with formatting
            text_blocks = self._extract_text_blocks(document)
            if text_blocks:
                structured_data['text_blocks'] = text_blocks
            
            return structured_data
            
        except Exception as e:
            logger.error(f"Error extracting structured data with Docling: {str(e)}")
            return {}
    
    def _extract_tables(self, document) -> list:
        """Extract table data from document."""
        tables = []
        
        try:
            if hasattr(document, 'body'):
                for element in document.body:
                    if hasattr(element, 'type') and 'table' in str(element.type).lower():
                        table_data = self._process_table_element(element)
                        if table_data:
                            tables.append(table_data)
            
        except Exception as e:
            logger.error(f"Error extracting tables: {str(e)}")
        
        return tables
    
    def _extract_text_blocks(self, document) -> list:
        """Extract text blocks with formatting information."""
        text_blocks = []
        
        try:
            if hasattr(document, 'body'):
                for element in document.body:
                    block = {
                        'text': '',
                        'type': str(getattr(element, 'type', 'unknown')),
                        'confidence': getattr(element, 'confidence', 1.0)
                    }
                    
                    if hasattr(element, 'text'):
                        block['text'] = element.text
                    elif hasattr(element, 'content'):
                        block['text'] = str(element.content)
                    
                    if block['text'].strip():
                        text_blocks.append(block)
            
        except Exception as e:
            logger.error(f"Error extracting text blocks: {str(e)}")
        
        return text_blocks
    
    def _process_table_element(self, table_element) -> Optional[Dict[str, Any]]:
        """Process a table element and extract data."""
        try:
            table_data = {
                'rows': [],
                'headers': [],
                'confidence': getattr(table_element, 'confidence', 1.0)
            }
            
            # Extract table content based on Docling structure
            if hasattr(table_element, 'rows'):
                for row in table_element.rows:
                    row_data = []
                    if hasattr(row, 'cells'):
                        for cell in row.cells:
                            cell_text = getattr(cell, 'text', str(cell))
                            row_data.append(cell_text)
                    table_data['rows'].append(row_data)
            
            return table_data if table_data['rows'] else None
            
        except Exception as e:
            logger.error(f"Error processing table element: {str(e)}")
            return None
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = [line.strip() for line in text.split('\n')]
        lines = [line for line in lines if line]  # Remove empty lines
        
        # Join lines with single newlines
        cleaned = '\n'.join(lines)
        
        return cleaned
