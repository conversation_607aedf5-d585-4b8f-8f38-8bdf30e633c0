# Default configuration for Receipt Parser Pro

# Tesseract OCR Configuration
tesseract:
  cmd: "tesseract"
  config: "--oem 3 --psm 6"
  language: "eng"
  
# Docling Configuration
docling:
  model_path: null
  enable_ocr: true
  enable_table_detection: true
  enable_figure_detection: true

# Google Gemini AI Configuration
gemini:
  model: "gemini-1.5-flash"
  temperature: 0.1
  max_tokens: 4096

# Processing Configuration
processing:
  max_image_size: 4096
  confidence_threshold: 0.8
  consensus_threshold: 0.9
  timeout_seconds: 300

# Method Weights for Consensus
method_weights:
  tesseract: 0.25
  docling: 0.30
  docling_pdf: 0.35
  gemini_ai: 0.10

# Logging Configuration
logging:
  level: "INFO"
  file: "receipt_parser.log"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

# API Configuration
api:
  host: "0.0.0.0"
  port: 8000
  debug: false
  cors_origins: ["*"]
  
# File Processing
files:
  max_size_mb: 50
  supported_formats: [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".webp", ".pdf"]
  output_format: "json"
