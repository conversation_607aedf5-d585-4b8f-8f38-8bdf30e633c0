"""
Tests for individual parser implementations.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np

from receipt_parser.config import Config
from receipt_parser.models import ReceiptData


class TestTesseractParser:
    """Test cases for TesseractParser."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock config for testing."""
        config = Mock(spec=Config)
        config.tesseract_cmd = "tesseract"
        config.tesseract_config = "--oem 3 --psm 6"
        config.max_image_size = 4096
        return config
    
    @patch('receipt_parser.parsers.tesseract_parser.pytesseract')
    @patch('receipt_parser.parsers.tesseract_parser.cv2')
    def test_tesseract_parser_initialization(self, mock_cv2, mock_pytesseract, mock_config):
        """Test TesseractParser initialization."""
        from receipt_parser.parsers.tesseract_parser import TesseractParser
        
        parser = TesseractParser(mock_config)
        assert parser.config == mock_config
    
    @patch('receipt_parser.parsers.tesseract_parser.pytesseract')
    @patch('receipt_parser.parsers.tesseract_parser.cv2')
    def test_preprocess_image(self, mock_cv2, mock_pytesseract, mock_config):
        """Test image preprocessing."""
        from receipt_parser.parsers.tesseract_parser import TesseractParser
        
        # Mock image
        mock_image = np.zeros((100, 100, 3), dtype=np.uint8)
        
        # Mock cv2 functions
        mock_cv2.cvtColor.return_value = np.zeros((100, 100), dtype=np.uint8)
        mock_cv2.fastNlMeansDenoising.return_value = np.zeros((100, 100), dtype=np.uint8)
        mock_cv2.createCLAHE.return_value.apply.return_value = np.zeros((100, 100), dtype=np.uint8)
        mock_cv2.GaussianBlur.return_value = np.zeros((100, 100), dtype=np.uint8)
        mock_cv2.threshold.return_value = (127, np.zeros((100, 100), dtype=np.uint8))
        mock_cv2.morphologyEx.return_value = np.zeros((100, 100), dtype=np.uint8)
        mock_cv2.minAreaRect.return_value = ((50, 50), (80, 80), 0)
        
        parser = TesseractParser(mock_config)
        result = parser._preprocess_image(mock_image)
        
        assert result is not None
        assert isinstance(result, np.ndarray)


class TestGeminiParser:
    """Test cases for GeminiParser."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock config for testing."""
        config = Mock(spec=Config)
        config.google_api_key = "test_api_key"
        return config
    
    @patch('receipt_parser.parsers.gemini_parser.genai')
    def test_gemini_parser_initialization(self, mock_genai, mock_config):
        """Test GeminiParser initialization."""
        from receipt_parser.parsers.gemini_parser import GeminiParser
        
        mock_genai.GenerativeModel.return_value = Mock()
        
        parser = GeminiParser(mock_config)
        assert parser.config == mock_config
        mock_genai.configure.assert_called_once_with(api_key="test_api_key")
    
    @patch('receipt_parser.parsers.gemini_parser.genai')
    def test_parse_text_to_receipt(self, mock_genai, mock_config):
        """Test parsing text to receipt data."""
        from receipt_parser.parsers.gemini_parser import GeminiParser
        
        # Mock Gemini response
        mock_response = Mock()
        mock_response.text = '''
        {
            "merchant_name": "Test Store",
            "total_amount": "12.99",
            "transaction_date": "2025-06-30",
            "items": [
                {
                    "name": "Test Item",
                    "quantity": 1.0,
                    "unit_price": "12.99",
                    "total_price": "12.99"
                }
            ]
        }
        '''
        
        mock_model = Mock()
        mock_model.generate_content.return_value = mock_response
        mock_genai.GenerativeModel.return_value = mock_model
        
        parser = GeminiParser(mock_config)
        result = parser.parse_text_to_receipt("Test Store\nTotal: $12.99")
        
        assert isinstance(result, ReceiptData)
        assert result.merchant_name == "Test Store"
        assert result.total_amount == "12.99"
    
    @patch('receipt_parser.parsers.gemini_parser.genai')
    def test_parse_empty_text(self, mock_genai, mock_config):
        """Test parsing empty text."""
        from receipt_parser.parsers.gemini_parser import GeminiParser
        
        mock_genai.GenerativeModel.return_value = Mock()
        
        parser = GeminiParser(mock_config)
        result = parser.parse_text_to_receipt("")
        
        assert isinstance(result, ReceiptData)
        assert result.raw_text == ""


class TestDoclingParser:
    """Test cases for DoclingParser."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock config for testing."""
        config = Mock(spec=Config)
        config.docling_model_path = None
        return config
    
    def test_docling_parser_import_error(self, mock_config):
        """Test DoclingParser when Docling is not available."""
        with patch('receipt_parser.parsers.docling_parser.DocumentConverter', None):
            from receipt_parser.parsers.docling_parser import DoclingParser
            
            with pytest.raises(ImportError):
                DoclingParser(mock_config)
    
    @patch('receipt_parser.parsers.docling_parser.DocumentConverter')
    def test_docling_parser_initialization(self, mock_converter, mock_config):
        """Test DoclingParser initialization."""
        from receipt_parser.parsers.docling_parser import DoclingParser
        
        parser = DoclingParser(mock_config)
        assert parser.config == mock_config
        mock_converter.assert_called_once()


class TestDoclingPDFParser:
    """Test cases for DoclingPDFParser."""
    
    @pytest.fixture
    def mock_config(self):
        """Create mock config for testing."""
        config = Mock(spec=Config)
        config.docling_model_path = None
        return config
    
    def test_docling_pdf_parser_import_error(self, mock_config):
        """Test DoclingPDFParser when Docling is not available."""
        with patch('receipt_parser.parsers.docling_pdf_parser.DocumentConverter', None):
            from receipt_parser.parsers.docling_pdf_parser import DoclingPDFParser
            
            with pytest.raises(ImportError):
                DoclingPDFParser(mock_config)
    
    @patch('receipt_parser.parsers.docling_pdf_parser.DocumentConverter')
    @patch('receipt_parser.parsers.docling_pdf_parser.PdfPipelineOptions')
    def test_docling_pdf_parser_initialization(self, mock_options, mock_converter, mock_config):
        """Test DoclingPDFParser initialization."""
        from receipt_parser.parsers.docling_pdf_parser import DoclingPDFParser
        
        parser = DoclingPDFParser(mock_config)
        assert parser.config == mock_config
        mock_converter.assert_called_once()
    
    @patch('receipt_parser.parsers.docling_pdf_parser.DocumentConverter')
    @patch('receipt_parser.parsers.docling_pdf_parser.PdfPipelineOptions')
    def test_extract_text_non_pdf(self, mock_options, mock_converter, mock_config):
        """Test extracting text from non-PDF file."""
        from receipt_parser.parsers.docling_pdf_parser import DoclingPDFParser
        
        parser = DoclingPDFParser(mock_config)
        
        with pytest.raises(ValueError, match="File is not a PDF"):
            parser.extract_text(Path("test.jpg"))
