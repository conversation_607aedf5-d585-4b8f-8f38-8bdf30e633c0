#!/usr/bin/env python3
"""
Installation and testing script for Receipt Parser Pro.
"""

import os
import sys
import subprocess
import tempfile
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont


def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"   ✅ Success")
            return True
        else:
            print(f"   ❌ Failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return False


def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("   ❌ Python 3.8+ required")
        return False
    else:
        print(f"   ✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check Tesseract
    if run_command("tesseract --version", "Checking Tesseract"):
        print("   ✅ Tesseract OCR found")
    else:
        print("   ⚠️  Tesseract not found - install from https://github.com/UB-Mannheim/tesseract/wiki")
    
    # Check pip
    if run_command("pip --version", "Checking pip"):
        print("   ✅ pip found")
    else:
        print("   ❌ pip not found")
        return False
    
    return True


def install_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    # Install main dependencies
    if run_command("pip install -r requirements.txt", "Installing requirements"):
        print("   ✅ Main dependencies installed")
    else:
        print("   ❌ Failed to install dependencies")
        return False
    
    # Try to install optional dependencies
    print("📦 Installing optional dependencies...")
    
    # Try Docling (optional)
    if run_command("pip install docling", "Installing Docling (optional)"):
        print("   ✅ Docling installed")
    else:
        print("   ⚠️  Docling installation failed (optional)")
    
    # Try FastAPI for API (optional)
    if run_command("pip install fastapi uvicorn python-multipart", "Installing FastAPI (optional)"):
        print("   ✅ FastAPI installed")
    else:
        print("   ⚠️  FastAPI installation failed (optional)")
    
    return True


def create_sample_receipt():
    """Create a sample receipt image for testing."""
    print("🖼️  Creating sample receipt...")
    
    try:
        # Create sample receipt image
        width, height = 400, 600
        image = Image.new('RGB', (width, height), color='white')
        draw = ImageDraw.Draw(image)
        
        # Try to use a font, fallback to default
        try:
            font = ImageFont.truetype("arial.ttf", 16)
            small_font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
            small_font = ImageFont.load_default()
        
        # Draw receipt content
        y = 20
        lines = [
            "SAMPLE GROCERY STORE",
            "123 Main Street",
            "Test City, TC 12345",
            "(555) 123-4567",
            "",
            "Date: 06/30/2025  Time: 14:30",
            "Receipt #: TXN123456",
            "Cashier: John Doe",
            "",
            "ITEMS:",
            "Apples        2 @ $1.99  $3.98",
            "Bread                    $2.49",
            "Milk                     $3.29",
            "Bananas       3 @ $0.79  $2.37",
            "",
            "Subtotal:               $12.13",
            "Tax (8%):                $0.97",
            "TOTAL:                  $13.10",
            "",
            "Payment: Credit Card ****1234",
            "",
            "Thank you for shopping!",
            "Visit us again soon!"
        ]
        
        for line in lines:
            if line.startswith("SAMPLE GROCERY STORE"):
                draw.text((width//2 - 100, y), line, fill='black', font=font)
            elif line.startswith("TOTAL:"):
                draw.text((50, y), line, fill='black', font=font)
            else:
                draw.text((50, y), line, fill='black', font=small_font)
            y += 25
        
        # Save sample receipt
        samples_dir = Path("samples")
        samples_dir.mkdir(exist_ok=True)
        
        sample_path = samples_dir / "sample_receipt.jpg"
        image.save(sample_path, "JPEG", quality=95)
        
        print(f"   ✅ Sample receipt created: {sample_path}")
        return sample_path
        
    except Exception as e:
        print(f"   ❌ Failed to create sample receipt: {str(e)}")
        return None


def setup_environment():
    """Setup environment configuration."""
    print("⚙️  Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        # Copy example to .env
        env_content = env_example.read_text()
        env_file.write_text(env_content)
        print("   ✅ Created .env from .env.example")
        print("   ⚠️  Please edit .env and add your Google API key")
    elif env_file.exists():
        print("   ✅ .env file already exists")
    else:
        # Create basic .env
        env_content = """# Receipt Parser Pro Configuration
GOOGLE_API_KEY=your_google_api_key_here
TESSERACT_CMD=tesseract
LOG_LEVEL=INFO
"""
        env_file.write_text(env_content)
        print("   ✅ Created basic .env file")
        print("   ⚠️  Please edit .env and add your Google API key")
    
    return True


def run_tests():
    """Run basic tests."""
    print("🧪 Running tests...")
    
    # Check if pytest is available
    if not run_command("python -m pytest --version", "Checking pytest"):
        print("   Installing pytest...")
        if not run_command("pip install pytest", "Installing pytest"):
            print("   ❌ Failed to install pytest")
            return False
    
    # Run tests
    if run_command("python -m pytest tests/ -v", "Running test suite"):
        print("   ✅ All tests passed")
        return True
    else:
        print("   ⚠️  Some tests failed (this is normal if dependencies are missing)")
        return True  # Don't fail installation for test failures


def test_basic_functionality():
    """Test basic functionality without external dependencies."""
    print("🔬 Testing basic functionality...")
    
    try:
        # Test imports
        from receipt_parser.models import ReceiptData, ParsingMethod
        from receipt_parser.config import Config
        print("   ✅ Core imports successful")
        
        # Test model creation
        receipt = ReceiptData(
            merchant_name="Test Store",
            total_amount="12.99"
        )
        print("   ✅ Model creation successful")
        
        # Test configuration
        config = Config.load_config()
        print("   ✅ Configuration loading successful")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Basic functionality test failed: {str(e)}")
        return False


def main():
    """Main installation and testing routine."""
    print("🧾 Receipt Parser Pro - Installation & Testing")
    print("=" * 50)
    
    success = True
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed")
        success = False
    
    # Install dependencies
    if success and not install_dependencies():
        print("\n❌ Dependency installation failed")
        success = False
    
    # Setup environment
    if success and not setup_environment():
        print("\n❌ Environment setup failed")
        success = False
    
    # Test basic functionality
    if success and not test_basic_functionality():
        print("\n❌ Basic functionality test failed")
        success = False
    
    # Create sample receipt
    sample_path = create_sample_receipt()
    
    # Run tests
    if success:
        run_tests()
    
    # Final summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit .env file and add your Google API key")
        print("2. Test with: python -m receipt_parser.cli parse samples/sample_receipt.jpg")
        print("3. Start API with: python -m receipt_parser.cli serve")
        print("4. Check examples/ directory for usage examples")
        
        if sample_path:
            print(f"\n🖼️  Sample receipt created at: {sample_path}")
            print("   Use this for testing the parser")
    else:
        print("❌ Installation encountered issues")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure Python 3.8+ is installed")
        print("2. Install Tesseract OCR for your system")
        print("3. Check internet connection for pip installs")
        print("4. Run with administrator/sudo if needed")


if __name__ == "__main__":
    main()
