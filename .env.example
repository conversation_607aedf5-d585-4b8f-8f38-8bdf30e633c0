# Google Gemini AI API Key
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Tesseract Configuration
TESSERACT_CMD=tesseract  # Path to tesseract executable (leave default if in PATH)
TESSERACT_CONFIG=--oem 3 --psm 6  # OCR Engine Mode and Page Segmentation Mode

# Docling Configuration
DOCLING_MODEL_PATH=./models/docling  

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=receipt_parser.log

# API Configuration (if using REST API)
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=False

# Processing Configuration
MAX_IMAGE_SIZE=4096  # Maximum image dimension for processing
CONFIDENCE_THRESHOLD=0.8  # Minimum confidence for OCR results
CONSENSUS_THRESHOLD=0.9  # Minimum consensus score for final results
