"""
Command-line interface for Receipt Parser Pro.
"""

import json
import sys
from pathlib import Path
from typing import Optional, List
import click
from loguru import logger

from .core import ReceiptParser
from .config import Config
from .models import ParsingMethod
from .utils.file_handler import FileHandler


@click.group()
@click.option('--config', '-c', type=click.Path(exists=True), help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Enable verbose logging')
@click.pass_context
def cli(ctx, config: Optional[str], verbose: bool):
    """Receipt Parser Pro - Advanced receipt parsing with multiple OCR methods and AI validation."""
    
    # Configure logging
    log_level = "DEBUG" if verbose else "INFO"
    logger.remove()
    logger.add(sys.stderr, level=log_level, format="{time:HH:mm:ss} | {level} | {message}")
    
    # Load configuration
    try:
        if config:
            ctx.obj = Config.load_config(config)
        else:
            ctx.obj = Config.load_config()
        
        logger.info("Configuration loaded successfully")
        
    except Exception as e:
        logger.error(f"Error loading configuration: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('file_path', type=click.Path(exists=True))
@click.option('--methods', '-m', multiple=True,
              type=click.Choice(['tesseract', 'docling', 'docling_pdf', 'gemini_ai', 'all']),
              default=['all'], help='Parsing methods to use')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--no-consensus', is_flag=True, help='Disable consensus engine')
@click.option('--format', 'output_format', type=click.Choice(['json', 'yaml']), 
              default='json', help='Output format')
@click.pass_context
def parse(ctx, file_path: str, methods: tuple, output: Optional[str], 
          no_consensus: bool, output_format: str):
    """Parse a single receipt file."""
    
    config = ctx.obj
    file_path = Path(file_path)
    
    try:
        # Initialize parser
        parser = ReceiptParser(config)
        file_handler = FileHandler(config)
        
        # Validate file
        is_valid, error = file_handler.validate_file(file_path)
        if not is_valid:
            logger.error(f"Invalid file: {error}")
            sys.exit(1)
        
        # Determine parsing methods
        if 'all' in methods:
            file_type = file_handler.get_file_type(file_path)
            parsing_methods = parser._get_available_methods(file_type)
        else:
            parsing_methods = [ParsingMethod(method) for method in methods if method != 'all']
        
        logger.info(f"Parsing {file_path} using methods: {[m.value for m in parsing_methods]}")
        
        # Parse receipt
        result = parser.parse_receipt(
            file_path, 
            methods=parsing_methods, 
            use_consensus=not no_consensus
        )
        
        # Prepare output
        if hasattr(result, 'final_result'):  # ConsensusResult
            output_data = {
                'consensus_result': result.final_result.dict(),
                'individual_results': [r.dict() for r in result.individual_results],
                'consensus_score': result.consensus_score,
                'method_weights': result.method_weights
            }
        else:  # ParseResult
            output_data = result.dict()
        
        # Output results
        if output:
            output_path = Path(output)
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_format == 'json':
                    json.dump(output_data, f, indent=2, default=str)
                else:  # yaml
                    import yaml
                    yaml.dump(output_data, f, default_flow_style=False)
            
            logger.info(f"Results saved to: {output_path}")
        else:
            # Print to stdout
            if output_format == 'json':
                print(json.dumps(output_data, indent=2, default=str))
            else:
                import yaml
                print(yaml.dump(output_data, default_flow_style=False))
        
        # Print summary
        if hasattr(result, 'final_result'):
            final_result = result.final_result
        else:
            final_result = result
        
        if final_result.success:
            logger.info(f"✅ Parsing successful (confidence: {final_result.confidence:.2f})")
            if final_result.data:
                logger.info(f"📊 Extracted: {final_result.data.merchant_name or 'Unknown merchant'}")
                logger.info(f"💰 Total: {final_result.data.total_amount or 'Unknown'}")
        else:
            logger.error(f"❌ Parsing failed: {final_result.error_message}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error parsing receipt: {str(e)}")
        sys.exit(1)


@cli.command()
@click.argument('directory', type=click.Path(exists=True))
@click.option('--recursive', '-r', is_flag=True, help='Search recursively')
@click.option('--output-dir', '-o', type=click.Path(), help='Output directory')
@click.option('--methods', '-m', multiple=True,
              type=click.Choice(['tesseract', 'docling', 'docling_pdf', 'gemini_ai', 'all']),
              default=['all'], help='Parsing methods to use')
@click.option('--no-consensus', is_flag=True, help='Disable consensus engine')
@click.option('--format', 'output_format', type=click.Choice(['json', 'yaml']),
              default='json', help='Output format')
@click.pass_context
def batch(ctx, directory: str, recursive: bool, output_dir: Optional[str],
          methods: tuple, no_consensus: bool, output_format: str):
    """Parse multiple receipt files in a directory."""
    
    config = ctx.obj
    directory = Path(directory)
    
    try:
        # Initialize components
        parser = ReceiptParser(config)
        file_handler = FileHandler(config)
        
        # Find receipt files
        files = file_handler.find_receipt_files(directory, recursive)
        
        if not files:
            logger.warning("No supported receipt files found")
            return
        
        logger.info(f"Found {len(files)} files to process")
        
        # Setup output directory
        if output_dir:
            output_path = Path(output_dir)
            file_handler.ensure_directory(output_path)
        else:
            output_path = directory / "parsed_receipts"
            file_handler.ensure_directory(output_path)
        
        # Process files
        successful = 0
        failed = 0
        
        for file_path in files:
            try:
                logger.info(f"Processing: {file_path.name}")
                
                # Determine parsing methods
                if 'all' in methods:
                    file_type = file_handler.get_file_type(file_path)
                    parsing_methods = parser._get_available_methods(file_type)
                else:
                    parsing_methods = [ParsingMethod(method) for method in methods if method != 'all']
                
                # Parse receipt
                result = parser.parse_receipt(
                    file_path,
                    methods=parsing_methods,
                    use_consensus=not no_consensus
                )
                
                # Save result
                output_file = file_handler.create_output_filename(
                    file_path, 
                    suffix="_parsed", 
                    extension=f".{output_format}"
                )
                output_file = output_path / output_file.name
                
                # Prepare output data
                if hasattr(result, 'final_result'):  # ConsensusResult
                    output_data = {
                        'source_file': str(file_path),
                        'consensus_result': result.final_result.dict(),
                        'individual_results': [r.dict() for r in result.individual_results],
                        'consensus_score': result.consensus_score
                    }
                else:  # ParseResult
                    output_data = {
                        'source_file': str(file_path),
                        'result': result.dict()
                    }
                
                # Save to file
                with open(output_file, 'w', encoding='utf-8') as f:
                    if output_format == 'json':
                        json.dump(output_data, f, indent=2, default=str)
                    else:
                        import yaml
                        yaml.dump(output_data, f, default_flow_style=False)
                
                if (hasattr(result, 'final_result') and result.final_result.success) or result.success:
                    successful += 1
                    logger.info(f"✅ {file_path.name} - Success")
                else:
                    failed += 1
                    logger.warning(f"❌ {file_path.name} - Failed")
                
            except Exception as e:
                failed += 1
                logger.error(f"❌ {file_path.name} - Error: {str(e)}")
        
        # Summary
        logger.info(f"\n📊 Batch processing complete:")
        logger.info(f"   ✅ Successful: {successful}")
        logger.info(f"   ❌ Failed: {failed}")
        logger.info(f"   📁 Results saved to: {output_path}")
        
    except Exception as e:
        logger.error(f"Error in batch processing: {str(e)}")
        sys.exit(1)


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--reload', is_flag=True, help='Enable auto-reload')
@click.pass_context
def serve(ctx, host: str, port: int, reload: bool):
    """Start the REST API server."""
    
    config = ctx.obj
    
    try:
        import uvicorn
        from .api import create_app
        
        # Create FastAPI app
        app = create_app(config)
        
        logger.info(f"Starting API server on {host}:{port}")
        
        # Run server
        uvicorn.run(
            app,
            host=host,
            port=port,
            reload=reload,
            log_level="info"
        )
        
    except ImportError:
        logger.error("FastAPI and uvicorn are required for the API server")
        logger.error("Install with: pip install fastapi uvicorn")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")
        sys.exit(1)


def main():
    """Main entry point for CLI."""
    cli()


if __name__ == '__main__':
    main()
