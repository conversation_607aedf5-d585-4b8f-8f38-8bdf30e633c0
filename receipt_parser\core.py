"""
Core receipt parser implementation with multiple parsing methods.
"""

import time
import asyncio
from typing import Union, List, Optional
from pathlib import Path
from loguru import logger

from .config import Config
from .models import <PERSON><PERSON><PERSON><PERSON><PERSON>, ConsensusR<PERSON>ult, ParsingMethod, ReceiptData
from .parsers.tesseract_parser import TesseractParser
from .parsers.docling_parser import DoclingParser
from .parsers.docling_pdf_parser import DoclingPDFParser
from .parsers.gemini_parser import GeminiParser
from .utils.consensus import ConsensusEngine
from .utils.file_handler import FileHandler


class ReceiptParser:
    """
    Main receipt parser that combines multiple parsing methods for maximum accuracy.
    """
    
    def __init__(self, config: Optional[Config] = None):
        """
        Initialize the receipt parser.
        
        Args:
            config: Configuration object. If None, loads from environment.
        """
        self.config = config or Config.load_config()
        self.config.validate_config()
        
        # Initialize parsers
        self.tesseract_parser = TesseractParser(self.config)
        self.docling_parser = DoclingParser(self.config)
        self.docling_pdf_parser = DoclingPDFParser(self.config)
        self.gemini_parser = GeminiParser(self.config)
        
        # Initialize utilities
        self.consensus_engine = ConsensusEngine(self.config)
        self.file_handler = FileHandler(self.config)
        
        logger.info("Receipt parser initialized successfully")
    
    def parse_receipt(
        self, 
        file_path: Union[str, Path], 
        methods: Optional[List[ParsingMethod]] = None,
        use_consensus: bool = True
    ) -> Union[ParseResult, ConsensusResult]:
        """
        Parse a receipt using specified methods or all available methods.
        
        Args:
            file_path: Path to the receipt file (image or PDF)
            methods: List of parsing methods to use. If None, uses all methods.
            use_consensus: Whether to use consensus engine for final result
            
        Returns:
            ParseResult if single method or ConsensusResult if multiple methods
        """
        start_time = time.time()
        file_path = Path(file_path)
        
        if not file_path.exists():
            return ParseResult(
                success=False,
                method=ParsingMethod.CONSENSUS,
                confidence=0.0,
                error_message=f"File not found: {file_path}",
                processing_time=time.time() - start_time
            )
        
        # Determine file type and available methods
        file_type = self.file_handler.get_file_type(file_path)
        available_methods = self._get_available_methods(file_type)
        
        if methods is None:
            methods = available_methods
        else:
            # Filter methods based on file type compatibility
            methods = [m for m in methods if m in available_methods]
        
        if not methods:
            return ParseResult(
                success=False,
                method=ParsingMethod.CONSENSUS,
                confidence=0.0,
                error_message=f"No compatible parsing methods for file type: {file_type}",
                processing_time=time.time() - start_time
            )
        
        logger.info(f"Parsing {file_path} using methods: {methods}")
        
        # Parse using each method
        results = []
        for method in methods:
            try:
                result = self._parse_with_method(file_path, method)
                results.append(result)
                logger.info(f"{method} parsing completed with confidence: {result.confidence}")
            except Exception as e:
                logger.error(f"Error parsing with {method}: {str(e)}")
                results.append(ParseResult(
                    success=False,
                    method=method,
                    confidence=0.0,
                    error_message=str(e),
                    processing_time=0.0
                ))
        
        # Return single result or consensus
        if len(results) == 1:
            return results[0]
        elif use_consensus:
            return self.consensus_engine.generate_consensus(results)
        else:
            # Return the result with highest confidence
            best_result = max(results, key=lambda r: r.confidence if r.success else 0.0)
            return best_result
    
    def _get_available_methods(self, file_type: str) -> List[ParsingMethod]:
        """Get available parsing methods based on file type."""
        if file_type.lower() == 'pdf':
            return [
                ParsingMethod.DOCLING_PDF,
                ParsingMethod.TESSERACT,  # Can handle PDF via pdf2image
                ParsingMethod.DOCLING,
                ParsingMethod.GEMINI_AI
            ]
        else:  # Image files
            return [
                ParsingMethod.TESSERACT,
                ParsingMethod.DOCLING,
                ParsingMethod.GEMINI_AI
            ]
    
    def _parse_with_method(self, file_path: Path, method: ParsingMethod) -> ParseResult:
        """Parse receipt using a specific method."""
        start_time = time.time()
        
        try:
            if method == ParsingMethod.TESSERACT:
                raw_text = self.tesseract_parser.extract_text(file_path)
                receipt_data = self.gemini_parser.parse_text_to_receipt(raw_text)
                
            elif method == ParsingMethod.DOCLING:
                raw_text = self.docling_parser.extract_text(file_path)
                receipt_data = self.gemini_parser.parse_text_to_receipt(raw_text)
                
            elif method == ParsingMethod.DOCLING_PDF:
                raw_text = self.docling_pdf_parser.extract_text(file_path)
                receipt_data = self.gemini_parser.parse_text_to_receipt(raw_text)

            elif method == ParsingMethod.GEMINI_AI:
                # Use Tesseract to extract text, then Gemini AI to parse it
                raw_text = self.tesseract_parser.extract_text(file_path)
                receipt_data = self.gemini_parser.parse_text_to_receipt(raw_text)

            else:
                raise ValueError(f"Unknown parsing method: {method}")
            
            # Calculate confidence based on data completeness
            confidence = self._calculate_confidence(receipt_data, raw_text)
            
            return ParseResult(
                success=True,
                method=method,
                confidence=confidence,
                data=receipt_data,
                processing_time=time.time() - start_time,
                raw_output=raw_text
            )
            
        except Exception as e:
            return ParseResult(
                success=False,
                method=method,
                confidence=0.0,
                error_message=str(e),
                processing_time=time.time() - start_time
            )
    
    def _calculate_confidence(self, receipt_data: ReceiptData, raw_text: str) -> float:
        """Calculate confidence score based on extracted data completeness."""
        if not receipt_data:
            return 0.0
        
        score = 0.0
        max_score = 0.0
        
        # Essential fields (higher weight)
        essential_fields = [
            ('merchant_name', 0.2),
            ('total_amount', 0.3),
            ('transaction_date', 0.2),
            ('items', 0.2)
        ]
        
        for field, weight in essential_fields:
            max_score += weight
            value = getattr(receipt_data, field, None)
            if value:
                if field == 'items' and len(value) > 0:
                    score += weight
                elif field != 'items' and value:
                    score += weight
        
        # Optional fields (lower weight)
        optional_fields = [
            'merchant_address', 'receipt_number', 'subtotal', 
            'tax_amount', 'payment_method'
        ]
        
        for field in optional_fields:
            max_score += 0.02
            if getattr(receipt_data, field, None):
                score += 0.02
        
        # Text quality bonus
        if raw_text and len(raw_text.strip()) > 50:
            score += 0.1
            max_score += 0.1
        
        return min(score / max_score if max_score > 0 else 0.0, 1.0)
