"""
Google Gemini AI parser for intelligent receipt data extraction and validation.
"""

import json
import re
from typing import Optional, Dict, Any
from datetime import datetime
from loguru import logger

try:
    import google.generativeai as genai
except ImportError:
    logger.warning("Google Generative AI not available. Install with: pip install google-generativeai")
    genai = None

from ..config import Config
from ..models import ReceiptData, ReceiptItem


class GeminiParser:
    """
    Google Gemini AI parser for intelligent receipt data extraction.
    """
    
    def __init__(self, config: Config):
        """Initialize Gemini parser with configuration."""
        self.config = config
        
        if genai is None:
            raise ImportError("Google Generative AI is not installed. Please install with: pip install google-generativeai")
        
        if not config.google_api_key:
            raise ValueError("Google API key is required for Gemini parser")
        
        # Configure Gemini
        genai.configure(api_key=config.google_api_key)
        
        # Initialize model
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        logger.info("Gemini AI parser initialized")
    
    def parse_text_to_receipt(self, raw_text: str) -> ReceiptData:
        """
        Parse raw OCR text into structured receipt data using Gemini AI.
        
        Args:
            raw_text: Raw text extracted from receipt
            
        Returns:
            ReceiptData object with extracted information
        """
        try:
            if not raw_text or not raw_text.strip():
                logger.warning("Empty text provided to Gemini parser")
                return ReceiptData(raw_text=raw_text)
            
            logger.info("Processing text with Gemini AI")
            
            # Create prompt for receipt parsing
            prompt = self._create_parsing_prompt(raw_text)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            if not response or not response.text:
                logger.warning("No response from Gemini AI")
                return ReceiptData(raw_text=raw_text)
            
            # Parse JSON response
            receipt_data = self._parse_gemini_response(response.text, raw_text)
            
            logger.info("Successfully parsed receipt with Gemini AI")
            return receipt_data
            
        except Exception as e:
            logger.error(f"Error parsing text with Gemini AI: {str(e)}")
            return ReceiptData(raw_text=raw_text)
    
    def _create_parsing_prompt(self, raw_text: str) -> str:
        """Create a detailed prompt for receipt parsing."""
        prompt = f"""
You are an expert receipt parser. Analyze the following receipt text and extract structured information.

RECEIPT TEXT:
{raw_text}

Extract the following information and return it as a JSON object with these exact fields:

{{
    "merchant_name": "Name of the store/merchant",
    "merchant_address": "Store address if available",
    "merchant_phone": "Store phone number if available",
    "transaction_date": "Date in YYYY-MM-DD format",
    "transaction_time": "Time in HH:MM format",
    "receipt_number": "Receipt or transaction number",
    "items": [
        {{
            "name": "Item name/description",
            "quantity": 1.0,
            "unit_price": "0.00",
            "total_price": "0.00",
            "category": "Item category if determinable"
        }}
    ],
    "subtotal": "Subtotal amount",
    "tax_amount": "Tax amount",
    "tip_amount": "Tip amount if any",
    "discount_amount": "Discount amount if any",
    "total_amount": "Final total amount",
    "payment_method": "Payment method (cash, card, etc.)",
    "card_last_four": "Last 4 digits of card if visible",
    "currency": "Currency code (USD, EUR, etc.)"
}}

IMPORTANT INSTRUCTIONS:
1. Extract only information that is clearly visible in the text
2. Use null for missing information
3. For monetary amounts, include only the numeric value (e.g., "12.99" not "$12.99")
4. For dates, use YYYY-MM-DD format
5. For times, use HH:MM format (24-hour)
6. Be precise and accurate - do not guess or infer information not present
7. If multiple items are listed, include all of them in the items array
8. Return only valid JSON, no additional text or explanations

JSON Response:
"""
        return prompt
    
    def _parse_gemini_response(self, response_text: str, raw_text: str) -> ReceiptData:
        """Parse Gemini AI response into ReceiptData object."""
        try:
            # Clean response text to extract JSON
            json_text = self._extract_json_from_response(response_text)
            
            if not json_text:
                logger.warning("No valid JSON found in Gemini response")
                return ReceiptData(raw_text=raw_text)
            
            # Parse JSON
            data = json.loads(json_text)
            
            # Convert to ReceiptData object
            receipt_data = self._convert_to_receipt_data(data, raw_text)
            
            return receipt_data
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in Gemini response: {str(e)}")
            return ReceiptData(raw_text=raw_text)
        except Exception as e:
            logger.error(f"Error parsing Gemini response: {str(e)}")
            return ReceiptData(raw_text=raw_text)
    
    def _extract_json_from_response(self, response_text: str) -> Optional[str]:
        """Extract JSON from Gemini response text."""
        # Try to find JSON block
        json_pattern = r'\{.*\}'
        match = re.search(json_pattern, response_text, re.DOTALL)
        
        if match:
            return match.group(0)
        
        # If no JSON block found, try the entire response
        response_text = response_text.strip()
        if response_text.startswith('{') and response_text.endswith('}'):
            return response_text
        
        return None
    
    def _convert_to_receipt_data(self, data: Dict[str, Any], raw_text: str) -> ReceiptData:
        """Convert parsed JSON data to ReceiptData object."""
        try:
            # Parse transaction date
            transaction_date = None
            if data.get('transaction_date'):
                try:
                    transaction_date = datetime.strptime(data['transaction_date'], '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"Invalid date format: {data['transaction_date']}")
            
            # Parse items
            items = []
            if data.get('items') and isinstance(data['items'], list):
                for item_data in data['items']:
                    try:
                        item = ReceiptItem(
                            name=item_data.get('name', ''),
                            quantity=self._safe_float(item_data.get('quantity')),
                            unit_price=self._safe_decimal_str(item_data.get('unit_price')),
                            total_price=self._safe_decimal_str(item_data.get('total_price')),
                            category=item_data.get('category')
                        )
                        items.append(item)
                    except Exception as e:
                        logger.warning(f"Error parsing item: {str(e)}")
            
            # Create ReceiptData object
            receipt_data = ReceiptData(
                merchant_name=data.get('merchant_name'),
                merchant_address=data.get('merchant_address'),
                merchant_phone=data.get('merchant_phone'),
                transaction_date=transaction_date,
                transaction_time=data.get('transaction_time'),
                receipt_number=data.get('receipt_number'),
                items=items,
                subtotal=self._safe_decimal_str(data.get('subtotal')),
                tax_amount=self._safe_decimal_str(data.get('tax_amount')),
                tip_amount=self._safe_decimal_str(data.get('tip_amount')),
                discount_amount=self._safe_decimal_str(data.get('discount_amount')),
                total_amount=self._safe_decimal_str(data.get('total_amount')),
                payment_method=data.get('payment_method'),
                card_last_four=data.get('card_last_four'),
                currency=data.get('currency', 'USD'),
                raw_text=raw_text
            )
            
            return receipt_data
            
        except Exception as e:
            logger.error(f"Error converting to ReceiptData: {str(e)}")
            return ReceiptData(raw_text=raw_text)
    
    def _safe_float(self, value) -> Optional[float]:
        """Safely convert value to float."""
        if value is None:
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _safe_decimal_str(self, value) -> Optional[str]:
        """Safely convert value to decimal string."""
        if value is None:
            return None
        try:
            # Convert to string and clean
            str_value = str(value).strip()
            if not str_value:
                return None
            
            # Remove currency symbols
            cleaned = re.sub(r'[^\d.-]', '', str_value)
            
            # Validate it's a valid number
            float(cleaned)
            return cleaned
        except (ValueError, TypeError):
            return None
    
    def validate_receipt_data(self, receipt_data: ReceiptData) -> Dict[str, Any]:
        """
        Validate and score receipt data quality using Gemini AI.
        
        Args:
            receipt_data: ReceiptData object to validate
            
        Returns:
            Dictionary with validation results and quality score
        """
        try:
            # Create validation prompt
            prompt = self._create_validation_prompt(receipt_data)
            
            # Generate response
            response = self.model.generate_content(prompt)
            
            if not response or not response.text:
                return {"valid": False, "score": 0.0, "issues": ["No validation response"]}
            
            # Parse validation response
            validation_result = self._parse_validation_response(response.text)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error validating receipt data: {str(e)}")
            return {"valid": False, "score": 0.0, "issues": [str(e)]}
    
    def _create_validation_prompt(self, receipt_data: ReceiptData) -> str:
        """Create prompt for receipt data validation."""
        data_dict = receipt_data.dict()
        
        prompt = f"""
Validate the following extracted receipt data for accuracy and completeness:

{json.dumps(data_dict, indent=2, default=str)}

Analyze the data and return a JSON response with:
{{
    "valid": true/false,
    "score": 0.0-1.0,
    "issues": ["list of any issues found"],
    "suggestions": ["list of suggestions for improvement"]
}}

Check for:
1. Data consistency (do item totals add up?)
2. Required fields presence
3. Format validity (dates, amounts, etc.)
4. Logical consistency (tax rates, etc.)

JSON Response:
"""
        return prompt
    
    def _parse_validation_response(self, response_text: str) -> Dict[str, Any]:
        """Parse validation response from Gemini."""
        try:
            json_text = self._extract_json_from_response(response_text)
            if json_text:
                return json.loads(json_text)
        except:
            pass
        
        # Fallback validation
        return {"valid": True, "score": 0.8, "issues": [], "suggestions": []}
