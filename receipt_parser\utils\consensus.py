"""
Consensus engine for combining results from multiple parsing methods.
"""

import json
from typing import List, Dict, Any, Optional
from collections import defaultdict
from loguru import logger

from ..config import Config
from ..models import Pa<PERSON><PERSON><PERSON><PERSON>, ConsensusR<PERSON>ult, <PERSON>rsingMethod, ReceiptData, ReceiptItem


class ConsensusEngine:
    """
    Engine for generating consensus results from multiple parsing methods.
    """
    
    def __init__(self, config: Config):
        """Initialize consensus engine with configuration."""
        self.config = config
        
        # Method weights for consensus calculation
        self.method_weights = {
            ParsingMethod.TESSERACT: 0.25,
            ParsingMethod.DOCLING: 0.30,
            ParsingMethod.DOCLING_PDF: 0.35,
            ParsingMethod.GEMINI_AI: 0.10  # Lower weight as it's used for processing
        }
        
        logger.info("Consensus engine initialized")
    
    def generate_consensus(self, results: List[ParseResult]) -> ConsensusResult:
        """
        Generate consensus result from multiple parsing results.
        
        Args:
            results: List of ParseResult objects from different methods
            
        Returns:
            ConsensusResult with final consensus
        """
        try:
            if not results:
                raise ValueError("No results provided for consensus")
            
            # Filter successful results
            successful_results = [r for r in results if r.success and r.data]
            
            if not successful_results:
                # Return the best failed result
                best_result = max(results, key=lambda r: r.confidence)
                return ConsensusResult(
                    final_result=best_result,
                    individual_results=results,
                    consensus_score=0.0,
                    method_weights=self.method_weights
                )
            
            logger.info(f"Generating consensus from {len(successful_results)} successful results")
            
            # Generate consensus data
            consensus_data = self._merge_receipt_data(successful_results)
            
            # Calculate consensus confidence
            consensus_confidence = self._calculate_consensus_confidence(successful_results)
            
            # Create final result
            final_result = ParseResult(
                success=True,
                method=ParsingMethod.CONSENSUS,
                confidence=consensus_confidence,
                data=consensus_data,
                processing_time=sum(r.processing_time for r in results),
                raw_output=self._combine_raw_outputs(results)
            )
            
            return ConsensusResult(
                final_result=final_result,
                individual_results=results,
                consensus_score=consensus_confidence,
                method_weights=self.method_weights
            )
            
        except Exception as e:
            logger.error(f"Error generating consensus: {str(e)}")
            # Return best individual result as fallback
            best_result = max(results, key=lambda r: r.confidence if r.success else 0.0)
            return ConsensusResult(
                final_result=best_result,
                individual_results=results,
                consensus_score=0.0,
                method_weights=self.method_weights
            )
    
    def _merge_receipt_data(self, results: List[ParseResult]) -> ReceiptData:
        """Merge receipt data from multiple successful results."""
        # Collect all data points
        data_points = defaultdict(list)
        
        for result in results:
            if result.data:
                receipt_dict = result.data.dict()
                weight = self.method_weights.get(result.method, 0.1)
                
                for field, value in receipt_dict.items():
                    if value is not None:
                        data_points[field].append((value, weight, result.confidence))
        
        # Merge data using weighted consensus
        merged_data = {}
        
        for field, values in data_points.items():
            if field == 'items':
                merged_data[field] = self._merge_items(values)
            elif field == 'raw_text':
                # Combine raw text from all sources
                raw_texts = [v[0] for v in values if v[0]]
                merged_data[field] = '\n\n---\n\n'.join(raw_texts) if raw_texts else None
            else:
                merged_data[field] = self._merge_field_values(values)
        
        # Create ReceiptData object
        return ReceiptData(**merged_data)
    
    def _merge_field_values(self, values: List[tuple]) -> Any:
        """Merge field values using weighted consensus."""
        if not values:
            return None
        
        if len(values) == 1:
            return values[0][0]
        
        # For string fields, use the most confident value
        if isinstance(values[0][0], str):
            return max(values, key=lambda x: x[1] * x[2])[0]
        
        # For numeric fields, use weighted average
        if isinstance(values[0][0], (int, float)):
            total_weight = sum(weight * confidence for _, weight, confidence in values)
            if total_weight > 0:
                weighted_sum = sum(value * weight * confidence for value, weight, confidence in values)
                return weighted_sum / total_weight
        
        # For other types, use the most confident value
        return max(values, key=lambda x: x[1] * x[2])[0]
    
    def _merge_items(self, item_lists: List[tuple]) -> List[ReceiptItem]:
        """Merge item lists from different parsing results."""
        if not item_lists:
            return []
        
        # Get the most comprehensive item list
        best_items = max(item_lists, key=lambda x: len(x[0]) if x[0] else 0)[0]
        
        if not best_items:
            return []
        
        # For now, return the best item list
        # TODO: Implement more sophisticated item merging
        return best_items
    
    def _calculate_consensus_confidence(self, results: List[ParseResult]) -> float:
        """Calculate overall consensus confidence score."""
        if not results:
            return 0.0
        
        # Weighted average of individual confidences
        total_weight = 0.0
        weighted_confidence = 0.0
        
        for result in results:
            weight = self.method_weights.get(result.method, 0.1)
            total_weight += weight
            weighted_confidence += result.confidence * weight
        
        base_confidence = weighted_confidence / total_weight if total_weight > 0 else 0.0
        
        # Bonus for agreement between methods
        agreement_bonus = self._calculate_agreement_bonus(results)
        
        # Final confidence with agreement bonus
        final_confidence = min(base_confidence + agreement_bonus, 1.0)
        
        return final_confidence
    
    def _calculate_agreement_bonus(self, results: List[ParseResult]) -> float:
        """Calculate bonus for agreement between parsing methods."""
        if len(results) < 2:
            return 0.0
        
        # Compare key fields across results
        key_fields = ['merchant_name', 'total_amount', 'transaction_date']
        agreements = 0
        comparisons = 0
        
        for i in range(len(results)):
            for j in range(i + 1, len(results)):
                if results[i].data and results[j].data:
                    for field in key_fields:
                        val1 = getattr(results[i].data, field, None)
                        val2 = getattr(results[j].data, field, None)
                        
                        if val1 is not None and val2 is not None:
                            comparisons += 1
                            if self._values_agree(val1, val2):
                                agreements += 1
        
        if comparisons == 0:
            return 0.0
        
        agreement_ratio = agreements / comparisons
        return agreement_ratio * 0.1  # Max 10% bonus
    
    def _values_agree(self, val1: Any, val2: Any) -> bool:
        """Check if two values agree (with some tolerance for numeric values)."""
        if val1 == val2:
            return True
        
        # For numeric values, allow small differences
        if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
            return abs(val1 - val2) < 0.01
        
        # For strings, check similarity
        if isinstance(val1, str) and isinstance(val2, str):
            # Simple similarity check
            return val1.lower().strip() == val2.lower().strip()
        
        return False
    
    def _combine_raw_outputs(self, results: List[ParseResult]) -> str:
        """Combine raw outputs from all parsing methods."""
        outputs = []
        
        for result in results:
            if result.raw_output:
                method_name = result.method.value.upper()
                outputs.append(f"=== {method_name} OUTPUT ===\n{result.raw_output}")
        
        return '\n\n'.join(outputs) if outputs else ""
