#!/usr/bin/env python3
"""
Test script to verify basic imports work.
"""

def test_basic_imports():
    """Test basic imports without external dependencies."""
    print("🔍 Testing basic imports...")
    
    try:
        # Test core Python imports
        import sys
        import os
        from pathlib import Path
        print("   ✅ Core Python imports")
        
        # Test essential dependencies
        try:
            import loguru
            print("   ✅ loguru")
        except ImportError:
            print("   ❌ loguru - run: pip install loguru")
        
        try:
            import pydantic
            print("   ✅ pydantic")
        except ImportError:
            print("   ❌ pydantic - run: pip install pydantic")
        
        try:
            import click
            print("   ✅ click")
        except ImportError:
            print("   ❌ click - run: pip install click")
        
        try:
            import yaml
            print("   ✅ pyyaml")
        except ImportError:
            print("   ❌ pyyaml - run: pip install pyyaml")
        
        try:
            import dotenv
            print("   ✅ python-dotenv")
        except ImportError:
            print("   ❌ python-dotenv - run: pip install python-dotenv")
        
        # Test our package imports
        try:
            from receipt_parser.models import ReceiptData, ParsingMethod
            print("   ✅ receipt_parser.models")
        except ImportError as e:
            print(f"   ❌ receipt_parser.models - {e}")
        
        try:
            from receipt_parser.config import Config
            print("   ✅ receipt_parser.config")
        except ImportError as e:
            print(f"   ❌ receipt_parser.config - {e}")
        
        print("\n🧪 Testing basic functionality...")
        
        # Test model creation
        try:
            receipt = ReceiptData(
                merchant_name="Test Store",
                total_amount="12.99"
            )
            print("   ✅ ReceiptData model creation")
        except Exception as e:
            print(f"   ❌ ReceiptData model creation - {e}")
        
        # Test enum
        try:
            method = ParsingMethod.TESSERACT
            print("   ✅ ParsingMethod enum")
        except Exception as e:
            print(f"   ❌ ParsingMethod enum - {e}")
        
        # Test config
        try:
            config = Config.load_config()
            print("   ✅ Config creation")
        except Exception as e:
            print(f"   ❌ Config creation - {e}")
        
        print("\n✅ Basic import test completed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import test failed: {e}")
        return False


def test_optional_imports():
    """Test optional dependencies."""
    print("\n🔍 Testing optional imports...")
    
    optional_deps = [
        ("pytesseract", "Tesseract OCR"),
        ("cv2", "OpenCV (opencv-python)"),
        ("PIL", "Pillow"),
        ("google.generativeai", "Google Generative AI"),
        ("fastapi", "FastAPI"),
        ("numpy", "NumPy"),
    ]
    
    for module, name in optional_deps:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ⚠️  {name} - optional")


if __name__ == "__main__":
    print("🧾 Receipt Parser Pro - Import Test")
    print("=" * 40)
    
    success = test_basic_imports()
    test_optional_imports()
    
    if success:
        print("\n🎉 Basic functionality is working!")
        print("\n💡 Next steps:")
        print("1. Install missing dependencies: pip install -r requirements.txt")
        print("2. Set up environment: copy .env.example to .env")
        print("3. Add your Google API key to .env")
        print("4. Test with: python -m receipt_parser.cli --help")
    else:
        print("\n❌ Some basic imports failed")
        print("Install minimal dependencies: pip install -r requirements-minimal.txt")
