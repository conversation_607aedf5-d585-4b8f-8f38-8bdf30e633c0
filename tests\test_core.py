"""
Tests for core receipt parser functionality.
"""

import pytest
from pathlib import Path
from unittest.mock import Mock, patch
import tempfile
import json

from receipt_parser.core import ReceiptParser
from receipt_parser.config import Config
from receipt_parser.models import Parse<PERSON><PERSON><PERSON>, ParsingMethod, ReceiptData


class TestReceiptParser:
    """Test cases for ReceiptParser class."""
    
    @pytest.fixture
    def mock_config(self):
        """Create a mock configuration for testing."""
        config = Mock(spec=Config)
        config.google_api_key = "test_api_key"
        config.tesseract_cmd = "tesseract"
        config.tesseract_config = "--oem 3 --psm 6"
        config.max_image_size = 4096
        config.confidence_threshold = 0.8
        config.consensus_threshold = 0.9
        return config
    
    @pytest.fixture
    def sample_receipt_data(self):
        """Create sample receipt data for testing."""
        return ReceiptData(
            merchant_name="Test Store",
            total_amount="12.99",
            transaction_date="2025-06-30",
            items=[],
            raw_text="Test Store\nTotal: $12.99"
        )
    
    def test_parser_initialization(self, mock_config):
        """Test parser initialization."""
        with patch('receipt_parser.core.TesseractParser'), \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser'), \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler'):
            
            parser = ReceiptParser(mock_config)
            assert parser.config == mock_config
    
    def test_get_available_methods_pdf(self, mock_config):
        """Test getting available methods for PDF files."""
        with patch('receipt_parser.core.TesseractParser'), \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser'), \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler'):
            
            parser = ReceiptParser(mock_config)
            methods = parser._get_available_methods('pdf')
            
            expected_methods = [
                ParsingMethod.DOCLING_PDF,
                ParsingMethod.TESSERACT,
                ParsingMethod.DOCLING
            ]
            assert methods == expected_methods
    
    def test_get_available_methods_image(self, mock_config):
        """Test getting available methods for image files."""
        with patch('receipt_parser.core.TesseractParser'), \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser'), \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler'):
            
            parser = ReceiptParser(mock_config)
            methods = parser._get_available_methods('image')
            
            expected_methods = [
                ParsingMethod.TESSERACT,
                ParsingMethod.DOCLING
            ]
            assert methods == expected_methods
    
    def test_calculate_confidence(self, mock_config, sample_receipt_data):
        """Test confidence calculation."""
        with patch('receipt_parser.core.TesseractParser'), \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser'), \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler'):
            
            parser = ReceiptParser(mock_config)
            confidence = parser._calculate_confidence(sample_receipt_data, "Test Store\nTotal: $12.99")
            
            # Should have reasonable confidence for complete data
            assert 0.0 <= confidence <= 1.0
            assert confidence > 0.5  # Should be reasonably confident
    
    def test_parse_receipt_file_not_found(self, mock_config):
        """Test parsing non-existent file."""
        with patch('receipt_parser.core.TesseractParser'), \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser'), \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler'):
            
            parser = ReceiptParser(mock_config)
            result = parser.parse_receipt(Path("nonexistent.jpg"))
            
            assert not result.success
            assert "File not found" in result.error_message
    
    @patch('receipt_parser.core.Path.exists')
    def test_parse_receipt_success(self, mock_exists, mock_config, sample_receipt_data):
        """Test successful receipt parsing."""
        mock_exists.return_value = True
        
        with patch('receipt_parser.core.TesseractParser') as mock_tesseract, \
             patch('receipt_parser.core.DoclingParser'), \
             patch('receipt_parser.core.DoclingPDFParser'), \
             patch('receipt_parser.core.GeminiParser') as mock_gemini, \
             patch('receipt_parser.core.ConsensusEngine'), \
             patch('receipt_parser.core.FileHandler') as mock_file_handler:
            
            # Setup mocks
            mock_file_handler.return_value.get_file_type.return_value = 'image'
            mock_tesseract.return_value.extract_text.return_value = "Test receipt text"
            mock_gemini.return_value.parse_text_to_receipt.return_value = sample_receipt_data
            
            parser = ReceiptParser(mock_config)
            result = parser.parse_receipt(Path("test.jpg"), methods=[ParsingMethod.TESSERACT], use_consensus=False)
            
            assert result.success
            assert result.method == ParsingMethod.TESSERACT
            assert result.data == sample_receipt_data


class TestParseResult:
    """Test cases for ParseResult model."""
    
    def test_parse_result_creation(self):
        """Test creating a ParseResult."""
        result = ParseResult(
            success=True,
            method=ParsingMethod.TESSERACT,
            confidence=0.95,
            processing_time=1.5
        )
        
        assert result.success
        assert result.method == ParsingMethod.TESSERACT
        assert result.confidence == 0.95
        assert result.processing_time == 1.5
    
    def test_confidence_validation(self):
        """Test confidence score validation."""
        # Valid confidence
        result = ParseResult(
            success=True,
            method=ParsingMethod.TESSERACT,
            confidence=0.5,
            processing_time=1.0
        )
        assert result.confidence == 0.5
        
        # Invalid confidence should raise validation error
        with pytest.raises(ValueError):
            ParseResult(
                success=True,
                method=ParsingMethod.TESSERACT,
                confidence=1.5,  # Invalid: > 1.0
                processing_time=1.0
            )


class TestReceiptData:
    """Test cases for ReceiptData model."""
    
    def test_receipt_data_creation(self):
        """Test creating ReceiptData."""
        data = ReceiptData(
            merchant_name="Test Store",
            total_amount="12.99",
            currency="USD"
        )
        
        assert data.merchant_name == "Test Store"
        assert data.total_amount == "12.99"
        assert data.currency == "USD"
    
    def test_decimal_parsing(self):
        """Test decimal amount parsing."""
        data = ReceiptData(
            total_amount="$12.99",  # Should strip currency symbol
            tax_amount="1.50"
        )
        
        # The model should handle decimal conversion
        assert data.total_amount is not None
        assert data.tax_amount is not None
