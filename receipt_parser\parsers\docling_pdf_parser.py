"""
Docling PDF pipeline parser for advanced PDF receipt processing.
"""

from pathlib import Path
from typing import Optional, Dict, Any, List
from loguru import logger

try:
    from docling.document_converter import DocumentConverter
    from docling.datamodel.base_models import InputFormat
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.document_converter import PdfFormatOption
    from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend
except ImportError:
    logger.warning("Docling not available. Install with: pip install docling")
    DocumentConverter = None
    PdfPipelineOptions = None

from ..config import Config


class DoclingPDFParser:
    """
    Docling PDF pipeline parser for advanced PDF receipt processing.
    """
    
    def __init__(self, config: Config):
        """Initialize Docling PDF parser with configuration."""
        self.config = config
        
        if DocumentConverter is None:
            raise ImportError("Docling is not installed. Please install with: pip install docling")
        
        # Configure PDF pipeline options for optimal receipt processing
        pdf_options = self._configure_pdf_pipeline()
        
        # Initialize document converter with PDF-specific options
        self.converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(
                    pipeline_options=pdf_options,
                    backend=PyPdfiumDocumentBackend
                )
            }
        )
        
        logger.info("Docling PDF pipeline parser initialized")
    
    def _configure_pdf_pipeline(self) -> PdfPipelineOptions:
        """Configure PDF pipeline options for receipt processing."""
        if PdfPipelineOptions is None:
            return None
            
        # Configure pipeline for receipt-specific processing
        options = PdfPipelineOptions()
        
        # Enable OCR for scanned receipts
        options.do_ocr = True
        
        # Enable table detection (useful for itemized receipts)
        options.do_table_structure = True
        
        # Enable figure detection (for logos, etc.)
        options.do_figure = True
        
        # Configure OCR settings
        options.ocr_options = {
            'lang': 'eng',  # English language
            'psm': 6,       # Uniform block of text
            'oem': 3        # Default OCR Engine Mode
        }
        
        return options
    
    def extract_text(self, file_path: Path) -> str:
        """
        Extract text from PDF using Docling PDF pipeline.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Extracted text string
        """
        try:
            if file_path.suffix.lower() != '.pdf':
                raise ValueError(f"File is not a PDF: {file_path}")
            
            logger.info(f"Processing PDF with Docling pipeline: {file_path}")
            
            # Convert PDF document
            result = self.converter.convert(str(file_path))
            
            if not result or not result.document:
                logger.warning("No document content extracted from PDF")
                return ""
            
            # Extract text content with structure preservation
            text_content = self._extract_structured_text(result.document)
            
            logger.info(f"Extracted {len(text_content)} characters from PDF")
            return text_content
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF with Docling: {str(e)}")
            raise
    
    def _extract_structured_text(self, document) -> str:
        """
        Extract text while preserving document structure.
        
        Args:
            document: Docling document object
            
        Returns:
            Structured text content
        """
        text_parts = []
        
        try:
            # Process document body elements in order
            if hasattr(document, 'body') and document.body:
                for element in document.body:
                    element_text = self._process_document_element(element)
                    if element_text:
                        text_parts.append(element_text)
            
            # If no structured content, try alternative extraction
            if not text_parts:
                text_parts = self._fallback_text_extraction(document)
            
            # Join text parts with appropriate spacing
            full_text = self._join_text_parts(text_parts)
            
            return self._clean_text(full_text)
            
        except Exception as e:
            logger.error(f"Error extracting structured text: {str(e)}")
            return ""
    
    def _process_document_element(self, element) -> str:
        """Process individual document elements."""
        try:
            element_type = str(getattr(element, 'type', 'unknown')).lower()
            
            # Handle different element types
            if 'text' in element_type or 'paragraph' in element_type:
                return self._extract_text_element(element)
            elif 'table' in element_type:
                return self._extract_table_element(element)
            elif 'list' in element_type:
                return self._extract_list_element(element)
            elif 'title' in element_type or 'heading' in element_type:
                text = self._extract_text_element(element)
                return f"\n{text}\n" if text else ""
            else:
                # Generic text extraction
                return self._extract_text_element(element)
                
        except Exception as e:
            logger.error(f"Error processing document element: {str(e)}")
            return ""
    
    def _extract_text_element(self, element) -> str:
        """Extract text from text elements."""
        if hasattr(element, 'text') and element.text:
            return element.text.strip()
        elif hasattr(element, 'content'):
            return str(element.content).strip()
        elif hasattr(element, 'value'):
            return str(element.value).strip()
        return ""
    
    def _extract_table_element(self, element) -> str:
        """Extract and format table content."""
        try:
            table_text = []
            
            if hasattr(element, 'rows'):
                for row in element.rows:
                    row_cells = []
                    if hasattr(row, 'cells'):
                        for cell in row.cells:
                            cell_text = self._extract_text_element(cell)
                            row_cells.append(cell_text)
                    
                    if row_cells:
                        # Join cells with tabs for better structure
                        table_text.append('\t'.join(row_cells))
            
            return '\n'.join(table_text) if table_text else ""
            
        except Exception as e:
            logger.error(f"Error extracting table: {str(e)}")
            return ""
    
    def _extract_list_element(self, element) -> str:
        """Extract and format list content."""
        try:
            list_items = []
            
            if hasattr(element, 'items'):
                for item in element.items:
                    item_text = self._extract_text_element(item)
                    if item_text:
                        list_items.append(f"• {item_text}")
            
            return '\n'.join(list_items) if list_items else ""
            
        except Exception as e:
            logger.error(f"Error extracting list: {str(e)}")
            return ""
    
    def _fallback_text_extraction(self, document) -> List[str]:
        """Fallback text extraction method."""
        text_parts = []
        
        try:
            # Try to get raw text
            if hasattr(document, 'text'):
                text_parts.append(document.text)
            
            # Try to convert document to string
            if not text_parts:
                doc_str = str(document)
                if doc_str and doc_str != str(type(document)):
                    text_parts.append(doc_str)
            
        except Exception as e:
            logger.error(f"Error in fallback text extraction: {str(e)}")
        
        return text_parts
    
    def _join_text_parts(self, text_parts: List[str]) -> str:
        """Join text parts with appropriate spacing."""
        if not text_parts:
            return ""
        
        # Filter out empty parts
        non_empty_parts = [part.strip() for part in text_parts if part.strip()]
        
        # Join with double newlines to preserve structure
        return '\n\n'.join(non_empty_parts)
    
    def extract_pdf_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from PDF document.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Dictionary containing PDF metadata
        """
        try:
            result = self.converter.convert(str(file_path))
            
            if not result or not result.document:
                return {}
            
            metadata = {}
            
            # Extract document metadata
            if hasattr(result.document, 'metadata'):
                metadata.update(result.document.metadata)
            
            # Extract page information
            if hasattr(result.document, 'pages'):
                metadata['page_count'] = len(result.document.pages)
            
            # Extract processing information
            if hasattr(result, 'timings'):
                metadata['processing_time'] = result.timings
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting PDF metadata: {str(e)}")
            return {}
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize extracted text."""
        if not text:
            return ""
        
        # Remove excessive whitespace while preserving structure
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            cleaned_line = line.strip()
            if cleaned_line:
                cleaned_lines.append(cleaned_line)
            elif cleaned_lines and cleaned_lines[-1]:  # Preserve single empty lines
                cleaned_lines.append('')
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1]:
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
