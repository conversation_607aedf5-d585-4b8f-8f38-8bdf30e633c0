"""
Configuration management for receipt parser.
"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field


class Config(BaseModel):
    """Configuration settings for receipt parser."""
    
    # API Keys
    google_api_key: str = Field(..., description="Google Gemini AI API key")
    
    # Tesseract Configuration
    tesseract_cmd: str = Field(default="tesseract", description="Path to tesseract executable")
    tesseract_config: str = Field(default="--oem 3 --psm 6", description="Tesseract OCR configuration")
    
    # Docling Configuration
    docling_model_path: Optional[str] = Field(None, description="Path to Docling models")
    
    # Processing Configuration
    max_image_size: int = Field(default=4096, description="Maximum image dimension for processing")
    confidence_threshold: float = Field(default=0.8, description="Minimum confidence for OCR results")
    consensus_threshold: float = Field(default=0.9, description="Minimum consensus score for final results")
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: Optional[str] = Field(default="receipt_parser.log", description="Log file path")
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", description="API host")
    api_port: int = Field(default=8000, description="API port")
    api_debug: bool = Field(default=False, description="API debug mode")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @classmethod
    def load_config(cls, env_file: Optional[str] = None) -> "Config":
        """Load configuration from environment file."""
        if env_file:
            load_dotenv(env_file)
        else:
            # Try to find .env file in current directory or parent directories
            current_dir = Path.cwd()
            for parent in [current_dir] + list(current_dir.parents):
                env_path = parent / ".env"
                if env_path.exists():
                    load_dotenv(env_path)
                    break
        
        return cls(
            google_api_key=os.getenv("GOOGLE_API_KEY", ""),
            tesseract_cmd=os.getenv("TESSERACT_CMD", "tesseract"),
            tesseract_config=os.getenv("TESSERACT_CONFIG", "--oem 3 --psm 6"),
            docling_model_path=os.getenv("DOCLING_MODEL_PATH"),
            max_image_size=int(os.getenv("MAX_IMAGE_SIZE", "4096")),
            confidence_threshold=float(os.getenv("CONFIDENCE_THRESHOLD", "0.8")),
            consensus_threshold=float(os.getenv("CONSENSUS_THRESHOLD", "0.9")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_file=os.getenv("LOG_FILE", "receipt_parser.log"),
            api_host=os.getenv("API_HOST", "0.0.0.0"),
            api_port=int(os.getenv("API_PORT", "8000")),
            api_debug=os.getenv("API_DEBUG", "False").lower() == "true"
        )
    
    def validate_config(self) -> bool:
        """Validate configuration settings."""
        if not self.google_api_key:
            raise ValueError("Google API key is required")
        
        if not (0.0 <= self.confidence_threshold <= 1.0):
            raise ValueError("Confidence threshold must be between 0.0 and 1.0")
        
        if not (0.0 <= self.consensus_threshold <= 1.0):
            raise ValueError("Consensus threshold must be between 0.0 and 1.0")
        
        return True
