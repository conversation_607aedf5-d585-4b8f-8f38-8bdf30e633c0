#!/usr/bin/env python3
"""
Quick test script for Receipt Parser Pro.
"""

from receipt_parser import ReceiptParser, Config
from receipt_parser.models import ParsingMethod

def main():
    print("🧾 Receipt Parser Pro - Quick Test")
    print("=" * 40)
    
    try:
        # Load configuration
        config = Config.load_config()
        print("✅ Configuration loaded")
        
        # Initialize parser
        parser = ReceiptParser(config)
        print("✅ Parser initialized")
        
        # Parse a sample receipt
        receipt_path = "samples/sample_receipt.jpg"
        print(f"\n📄 Parsing: {receipt_path}")
        
        # Parse with Tesseract only (fastest)
        result = parser.parse_receipt(
            receipt_path, 
            methods=[ParsingMethod.TESSERACT],
            use_consensus=False
        )
        
        print(f"✅ Parsing completed!")
        print(f"📊 Confidence: {result.confidence:.2f}")
        print(f"🏪 Merchant: {result.data.merchant_name}")
        print(f"💰 Total: ${result.data.total_amount}")
        print(f"📝 Items found: {len(result.data.items)}")

        # Show items
        if result.data.items:
            print("\n🛒 Items:")
            for item in result.data.items[:3]:  # Show first 3 items
                print(f"  - {item.name}: ${item.price}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Test completed successfully!")
    else:
        print("\n❌ Test failed!")
