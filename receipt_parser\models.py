"""
Data models for receipt parsing results and configuration.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field, validator
from decimal import Decimal


class ParsingMethod(str, Enum):
    """Available parsing methods."""
    TESSERACT = "tesseract"
    DOCLING = "docling"
    DOCLING_PDF = "docling_pdf"
    GEMINI_AI = "gemini_ai"
    CONSENSUS = "consensus"


class ReceiptItem(BaseModel):
    """Individual item on a receipt."""
    name: str = Field(..., description="Item name/description")
    quantity: Optional[float] = Field(None, description="Quantity purchased")
    unit_price: Optional[Decimal] = Field(None, description="Price per unit")
    total_price: Optional[Decimal] = Field(None, description="Total price for this item")
    category: Optional[str] = Field(None, description="Item category")
    
    @validator('unit_price', 'total_price', pre=True)
    def parse_decimal(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            # Remove currency symbols and convert to decimal
            cleaned = v.replace('$', '').replace(',', '').strip()
            return Decimal(cleaned) if cleaned else None
        return Decimal(str(v))


class ReceiptData(BaseModel):
    """Structured receipt data."""
    # Merchant information
    merchant_name: Optional[str] = Field(None, description="Name of the merchant/store")
    merchant_address: Optional[str] = Field(None, description="Store address")
    merchant_phone: Optional[str] = Field(None, description="Store phone number")
    
    # Transaction details
    transaction_date: Optional[datetime] = Field(None, description="Date of transaction")
    transaction_time: Optional[str] = Field(None, description="Time of transaction")
    receipt_number: Optional[str] = Field(None, description="Receipt/transaction number")
    
    # Items
    items: List[ReceiptItem] = Field(default_factory=list, description="List of purchased items")
    
    # Financial totals
    subtotal: Optional[Decimal] = Field(None, description="Subtotal before tax")
    tax_amount: Optional[Decimal] = Field(None, description="Tax amount")
    tip_amount: Optional[Decimal] = Field(None, description="Tip amount")
    discount_amount: Optional[Decimal] = Field(None, description="Discount amount")
    total_amount: Optional[Decimal] = Field(None, description="Final total amount")
    
    # Payment information
    payment_method: Optional[str] = Field(None, description="Payment method used")
    card_last_four: Optional[str] = Field(None, description="Last 4 digits of card")
    
    # Additional fields
    currency: str = Field(default="USD", description="Currency code")
    raw_text: Optional[str] = Field(None, description="Raw extracted text")
    
    @validator('subtotal', 'tax_amount', 'tip_amount', 'discount_amount', 'total_amount', pre=True)
    def parse_decimal_amounts(cls, v):
        if v is None:
            return v
        if isinstance(v, str):
            cleaned = v.replace('$', '').replace(',', '').strip()
            return Decimal(cleaned) if cleaned else None
        return Decimal(str(v))


class ParseResult(BaseModel):
    """Result of parsing operation."""
    success: bool = Field(..., description="Whether parsing was successful")
    method: ParsingMethod = Field(..., description="Method used for parsing")
    confidence: float = Field(..., description="Confidence score (0.0 to 1.0)")
    data: Optional[ReceiptData] = Field(None, description="Extracted receipt data")
    error_message: Optional[str] = Field(None, description="Error message if parsing failed")
    processing_time: float = Field(..., description="Time taken for processing in seconds")
    raw_output: Optional[str] = Field(None, description="Raw output from parsing method")
    
    @validator('confidence')
    def validate_confidence(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Confidence must be between 0.0 and 1.0')
        return v


class ConsensusResult(BaseModel):
    """Result of consensus analysis from multiple parsing methods."""
    final_result: ParseResult = Field(..., description="Final consensus result")
    individual_results: List[ParseResult] = Field(..., description="Results from each method")
    consensus_score: float = Field(..., description="Overall consensus confidence")
    method_weights: Dict[ParsingMethod, float] = Field(..., description="Weights assigned to each method")
    
    @validator('consensus_score')
    def validate_consensus_score(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError('Consensus score must be between 0.0 and 1.0')
        return v
