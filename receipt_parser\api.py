"""
REST API for Receipt Parser Pro.
"""

import json
import tempfile
from pathlib import Path
from typing import List, Optional
from fastapi import FastAPI, File, UploadFile, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import BaseModel
from loguru import logger

from .core import ReceiptParser
from .config import Config
from .models import ParsingMethod, ParseResult, ConsensusResult
from .utils.file_handler import FileHandler


class ParseRequest(BaseModel):
    """Request model for parsing configuration."""
    methods: Optional[List[str]] = ["all"]
    use_consensus: bool = True


class ParseResponse(BaseModel):
    """Response model for parsing results."""
    success: bool
    result: dict
    processing_time: float
    message: Optional[str] = None


def create_app(config: Config) -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title="Receipt Parser Pro API",
        description="Advanced receipt parsing with multiple OCR methods and AI validation",
        version="1.0.0"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Initialize parser (singleton)
    parser = ReceiptParser(config)
    file_handler = FileHandler(config)
    
    @app.get("/")
    async def root():
        """Root endpoint with API information."""
        return {
            "name": "Receipt Parser Pro API",
            "version": "1.0.0",
            "description": "Advanced receipt parsing with multiple OCR methods and AI validation",
            "endpoints": {
                "parse": "/parse - Parse a single receipt file",
                "batch": "/batch - Parse multiple receipt files",
                "health": "/health - Health check",
                "methods": "/methods - Get available parsing methods"
            }
        }
    
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        try:
            # Basic health check
            return {
                "status": "healthy",
                "timestamp": "2025-06-30T16:00:00Z",
                "version": "1.0.0"
            }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Health check failed: {str(e)}")
    
    @app.get("/methods")
    async def get_methods():
        """Get available parsing methods."""
        return {
            "methods": [method.value for method in ParsingMethod],
            "supported_formats": file_handler.get_supported_extensions(),
            "description": {
                "tesseract": "Tesseract OCR with advanced image preprocessing",
                "docling": "Docling for intelligent document structure analysis",
                "docling_pdf": "Docling PDF pipeline for advanced PDF processing",
                "gemini_ai": "Google Gemini AI for intelligent data extraction",
                "consensus": "Consensus result from multiple methods"
            }
        }
    
    @app.post("/parse", response_model=ParseResponse)
    async def parse_receipt(
        file: UploadFile = File(...),
        methods: str = "all",
        use_consensus: bool = True
    ):
        """
        Parse a single receipt file.
        
        Args:
            file: Receipt file (image or PDF)
            methods: Comma-separated list of methods or 'all'
            use_consensus: Whether to use consensus engine
            
        Returns:
            Parsing results
        """
        temp_file = None
        
        try:
            # Validate file
            if not file.filename:
                raise HTTPException(status_code=400, detail="No file provided")
            
            # Check file extension
            file_path = Path(file.filename)
            if not file_handler.is_supported_file(file_path):
                raise HTTPException(
                    status_code=400, 
                    detail=f"Unsupported file type. Supported: {file_handler.get_supported_extensions()}"
                )
            
            # Save uploaded file to temporary location
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_path.suffix) as temp_file:
                content = await file.read()
                temp_file.write(content)
                temp_file_path = Path(temp_file.name)
            
            # Validate file
            is_valid, error = file_handler.validate_file(temp_file_path)
            if not is_valid:
                raise HTTPException(status_code=400, detail=f"Invalid file: {error}")
            
            # Determine parsing methods
            if methods == "all":
                file_type = file_handler.get_file_type(temp_file_path)
                parsing_methods = parser._get_available_methods(file_type)
            else:
                method_list = [m.strip() for m in methods.split(",")]
                try:
                    parsing_methods = [ParsingMethod(method) for method in method_list]
                except ValueError as e:
                    raise HTTPException(status_code=400, detail=f"Invalid method: {str(e)}")
            
            logger.info(f"Parsing {file.filename} using methods: {[m.value for m in parsing_methods]}")
            
            # Parse receipt
            result = parser.parse_receipt(
                temp_file_path,
                methods=parsing_methods,
                use_consensus=use_consensus
            )
            
            # Prepare response
            if hasattr(result, 'final_result'):  # ConsensusResult
                response_data = {
                    "type": "consensus",
                    "consensus_result": result.final_result.dict(),
                    "individual_results": [r.dict() for r in result.individual_results],
                    "consensus_score": result.consensus_score,
                    "method_weights": result.method_weights
                }
                success = result.final_result.success
                processing_time = result.final_result.processing_time
            else:  # ParseResult
                response_data = {
                    "type": "single",
                    "result": result.dict()
                }
                success = result.success
                processing_time = result.processing_time
            
            return ParseResponse(
                success=success,
                result=response_data,
                processing_time=processing_time,
                message="Parsing completed successfully" if success else "Parsing failed"
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error parsing receipt: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
        
        finally:
            # Clean up temporary file
            if temp_file and temp_file_path.exists():
                try:
                    temp_file_path.unlink()
                except:
                    pass
    
    @app.post("/batch")
    async def parse_batch(
        files: List[UploadFile] = File(...),
        methods: str = "all",
        use_consensus: bool = True
    ):
        """
        Parse multiple receipt files.
        
        Args:
            files: List of receipt files
            methods: Comma-separated list of methods or 'all'
            use_consensus: Whether to use consensus engine
            
        Returns:
            Batch parsing results
        """
        if not files:
            raise HTTPException(status_code=400, detail="No files provided")
        
        if len(files) > 10:  # Limit batch size
            raise HTTPException(status_code=400, detail="Maximum 10 files per batch")
        
        results = []
        temp_files = []
        
        try:
            # Process each file
            for file in files:
                temp_file_path = None
                
                try:
                    # Validate and save file
                    if not file.filename:
                        results.append({
                            "filename": "unknown",
                            "success": False,
                            "error": "No filename provided"
                        })
                        continue
                    
                    file_path = Path(file.filename)
                    if not file_handler.is_supported_file(file_path):
                        results.append({
                            "filename": file.filename,
                            "success": False,
                            "error": f"Unsupported file type: {file_path.suffix}"
                        })
                        continue
                    
                    # Save to temporary file
                    with tempfile.NamedTemporaryFile(delete=False, suffix=file_path.suffix) as temp_file:
                        content = await file.read()
                        temp_file.write(content)
                        temp_file_path = Path(temp_file.name)
                        temp_files.append(temp_file_path)
                    
                    # Validate file
                    is_valid, error = file_handler.validate_file(temp_file_path)
                    if not is_valid:
                        results.append({
                            "filename": file.filename,
                            "success": False,
                            "error": f"Invalid file: {error}"
                        })
                        continue
                    
                    # Determine parsing methods
                    if methods == "all":
                        file_type = file_handler.get_file_type(temp_file_path)
                        parsing_methods = parser._get_available_methods(file_type)
                    else:
                        method_list = [m.strip() for m in methods.split(",")]
                        parsing_methods = [ParsingMethod(method) for method in method_list]
                    
                    # Parse receipt
                    result = parser.parse_receipt(
                        temp_file_path,
                        methods=parsing_methods,
                        use_consensus=use_consensus
                    )
                    
                    # Add to results
                    if hasattr(result, 'final_result'):  # ConsensusResult
                        results.append({
                            "filename": file.filename,
                            "success": result.final_result.success,
                            "result": result.final_result.dict(),
                            "consensus_score": result.consensus_score
                        })
                    else:  # ParseResult
                        results.append({
                            "filename": file.filename,
                            "success": result.success,
                            "result": result.dict()
                        })
                
                except Exception as e:
                    results.append({
                        "filename": file.filename,
                        "success": False,
                        "error": str(e)
                    })
            
            # Summary
            successful = sum(1 for r in results if r["success"])
            failed = len(results) - successful
            
            return {
                "summary": {
                    "total": len(results),
                    "successful": successful,
                    "failed": failed
                },
                "results": results
            }
            
        except Exception as e:
            logger.error(f"Error in batch processing: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Batch processing error: {str(e)}")
        
        finally:
            # Clean up temporary files
            for temp_file_path in temp_files:
                try:
                    if temp_file_path.exists():
                        temp_file_path.unlink()
                except:
                    pass
    
    return app
